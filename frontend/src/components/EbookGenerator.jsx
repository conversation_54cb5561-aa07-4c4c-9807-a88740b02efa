import React, { useState } from 'react';
import { <PERSON><PERSON> } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Textarea } from './ui/textarea';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Progress } from './ui/progress';
import { Upload, FileText, Download, Eye, Wand2, BookOpen } from 'lucide-react';
import { useToast } from '../hooks/use-toast';
import EbookViewer from './EbookViewer';
import axios from 'axios';

const EbookGenerator = () => {
  const [inputText, setInputText] = useState('');
  const [title, setTitle] = useState('');
  const [author, setAuthor] = useState('');
  const [theme, setTheme] = useState('modern');
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [generatedEbook, setGeneratedEbook] = useState(null);
  const [viewMode, setViewMode] = useState('input'); // 'input', 'generating', 'viewer'
  const { toast } = useToast();

  const themes = [
    { value: 'modern', label: 'Modern', preview: 'Clean lines, minimal design' },
    { value: 'classic', label: 'Classic', preview: 'Traditional book styling' },
    { value: 'elegant', label: 'Elegant', preview: 'Sophisticated typography' },
    { value: 'creative', label: 'Creative', preview: 'Bold and artistic' },
    { value: 'academic', label: 'Academic', preview: 'Professional and formal' }
  ];

  const handleGenerate = async () => {
    if (!inputText.trim()) {
      toast({
        title: "Input Required",
        description: "Please enter some text to generate an ebook.",
        variant: "destructive"
      });
      return;
    }

    if (!title.trim()) {
      toast({
        title: "Title Required", 
        description: "Please enter a title for your ebook.",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    setViewMode('generating');
    setProgress(0);

    let progressInterval;
    try {
      // Create request payload
      const requestPayload = {
        title,
        author: author || undefined,
        content: inputText,
        theme,
        max_words_per_page: 300
      };

      // Simulate progress updates while making the API call
      progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev < 90) return prev + 10;
          return prev;
        });
      }, 1000);

      toast({
        title: "Processing",
        description: "Analyzing content structure with AI...",
      });

      // Make API call to backend
      const BACKEND_URL = process.env.REACT_APP_BACKEND_URL;
      const response = await axios.post(`${BACKEND_URL}/api/generate-ebook`, requestPayload);
      
      clearInterval(progressInterval);
      setProgress(100);

      // Transform backend response to match frontend expectations
      const backendEbook = response.data;
      const transformedEbook = {
        metadata: {
          title: backendEbook.metadata.title,
          author: backendEbook.metadata.author,
          theme: backendEbook.metadata.theme,
          generatedAt: backendEbook.metadata.generated_at,
          totalPages: backendEbook.metadata.total_pages,
          totalChapters: backendEbook.metadata.total_chapters,
          wordCount: backendEbook.metadata.word_count
        },
        chapters: backendEbook.chapters.map(chapter => ({
          id: chapter.id,
          title: chapter.title,
          startPage: chapter.start_page,
          endPage: chapter.end_page
        })),
        pages: backendEbook.pages.map(page => ({
          pageNumber: page.page_number,
          content: page.content,
          wordCount: page.word_count,
          chapter: page.chapter_id
        })),
        tableOfContents: backendEbook.table_of_contents.entries || []
      };

      setGeneratedEbook(transformedEbook);
      setViewMode('viewer');
      setIsGenerating(false);

      toast({
        title: "Ebook Generated Successfully!",
        description: `Your ${transformedEbook.pages.length}-page ebook is ready to view and download.`,
      });

    } catch (error) {
      console.error('Error generating ebook:', error);
      clearInterval(progressInterval);
      setIsGenerating(false);
      setViewMode('input');
      
      const errorMessage = error.response?.data?.detail || error.message || 'Unknown error occurred';
      
      toast({
        title: "Generation Failed",
        description: `Failed to generate ebook: ${errorMessage}`,
        variant: "destructive"
      });
    }
  };

  const handleReset = () => {
    setViewMode('input');
    setGeneratedEbook(null);
    setProgress(0);
  };

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Mock image handling - in real app this would upload to backend
      toast({
        title: "Image Added",
        description: `${file.name} will be included in your ebook.`,
      });
    }
  };

  if (viewMode === 'viewer' && generatedEbook) {
    return (
      <EbookViewer 
        ebook={generatedEbook} 
        onBack={handleReset}
        onDownload={() => {
          toast({
            title: "Download Started",
            description: "Your PDF is being prepared for download.",
          });
        }}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="bg-gradient-to-r from-indigo-600 to-cyan-600 p-3 rounded-xl">
              <BookOpen className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-cyan-600 bg-clip-text text-transparent">
              AI Ebook Generator
            </h1>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Transform your plain text into a beautifully formatted ebook with intelligent page splitting, 
            smart chapter divisions, and professional styling.
          </p>
        </div>

        {viewMode === 'generating' ? (
          <Card className="border-0 shadow-2xl bg-white/70 backdrop-blur-sm">
            <CardContent className="p-8">
              <div className="text-center space-y-6">
                <div className="w-20 h-20 mx-auto bg-gradient-to-r from-indigo-600 to-cyan-600 rounded-full flex items-center justify-center animate-pulse">
                  <Wand2 className="w-10 h-10 text-white" />
                </div>
                <h2 className="text-2xl font-semibold text-gray-800">
                  Generating Your Ebook
                </h2>
                <div className="space-y-3">
                  <Progress value={progress} className="w-full h-3" />
                  <p className="text-gray-600">
                    Our AI is working on your content... {progress}% complete
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="border-0 shadow-2xl bg-white/70 backdrop-blur-sm">
            <CardHeader className="pb-6">
              <CardTitle className="text-2xl font-semibold text-gray-800 flex items-center gap-2">
                <FileText className="w-6 h-6 text-indigo-600" />
                Create Your Ebook
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title" className="text-sm font-medium text-gray-700">
                    Ebook Title *
                  </Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Enter your ebook title"
                    className="transition-all duration-300 focus:scale-[1.02] focus:shadow-lg"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="author" className="text-sm font-medium text-gray-700">
                    Author
                  </Label>
                  <Input
                    id="author"
                    value={author}
                    onChange={(e) => setAuthor(e.target.value)}
                    placeholder="Your name"
                    className="transition-all duration-300 focus:scale-[1.02] focus:shadow-lg"
                  />
                </div>
              </div>

              {/* Theme Selection */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  Theme
                </Label>
                <Select value={theme} onValueChange={setTheme}>
                  <SelectTrigger className="transition-all duration-300 focus:scale-[1.02] focus:shadow-lg">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {themes.map((t) => (
                      <SelectItem key={t.value} value={t.value}>
                        <div>
                          <div className="font-medium">{t.label}</div>
                          <div className="text-xs text-gray-500">{t.preview}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Text Input */}
              <div className="space-y-2">
                <Label htmlFor="content" className="text-sm font-medium text-gray-700">
                  Content *
                </Label>
                <Textarea
                  id="content"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  placeholder="Paste or type your content here. The AI will automatically format it into a professional ebook with proper chapters, page breaks, and styling..."
                  className="min-h-[300px] transition-all duration-300 focus:scale-[1.01] focus:shadow-lg resize-none"
                />
                <div className="text-xs text-gray-500 text-right">
                  {inputText.length} characters
                </div>
              </div>

              {/* Image Upload */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  Add Images (Optional)
                </Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-indigo-500 transition-colors duration-300">
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600 mb-2">
                    Upload images to include in your ebook
                  </p>
                  <Input
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleImageUpload}
                    className="w-full"
                  />
                </div>
              </div>

              {/* Generate Button */}
              <Button
                onClick={handleGenerate}
                disabled={isGenerating}
                className="w-full py-6 text-lg font-medium bg-gradient-to-r from-indigo-600 to-cyan-600 hover:from-indigo-700 hover:to-cyan-700 transition-all duration-300 hover:scale-[1.02] hover:shadow-xl"
              >
                <Wand2 className="w-5 h-5 mr-2" />
                Generate AI Ebook
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default EbookGenerator;