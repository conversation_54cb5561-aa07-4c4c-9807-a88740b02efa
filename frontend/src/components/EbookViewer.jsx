import React, { useState } from 'react';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';
import { ChevronLeft, ChevronRight, Download, ArrowLeft, BookOpen, FileText, Settings } from 'lucide-react';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';

const EbookViewer = ({ ebook, onBack, onDownload }) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [viewMode, setViewMode] = useState('page'); // 'page' or 'scroll'

  const nextPage = () => {
    if (currentPage < ebook.pages.length - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToPage = (pageNum) => {
    setCurrentPage(pageNum);
  };

  const progress = ((currentPage + 1) / ebook.pages.length) * 100;

  const getThemeStyles = (theme) => {
    const themes = {
      modern: {
        bg: 'bg-white',
        text: 'text-gray-900',
        accent: 'text-indigo-600',
        border: 'border-gray-200'
      },
      classic: {
        bg: 'bg-amber-50',
        text: 'text-amber-900',
        accent: 'text-amber-700',
        border: 'border-amber-200'
      },
      elegant: {
        bg: 'bg-slate-50',
        text: 'text-slate-800',
        accent: 'text-slate-600',
        border: 'border-slate-300'
      },
      creative: {
        bg: 'bg-purple-50',
        text: 'text-purple-900',
        accent: 'text-purple-600',
        border: 'border-purple-200'
      },
      academic: {
        bg: 'bg-blue-50',
        text: 'text-blue-900',
        accent: 'text-blue-700',
        border: 'border-blue-200'
      }
    };
    return themes[theme] || themes.modern;
  };

  const themeStyles = getThemeStyles(ebook.metadata.theme);

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center gap-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Editor
              </Button>
              <div className="hidden md:flex items-center gap-2">
                <BookOpen className="w-5 h-5 text-indigo-600" />
                <span className="font-medium text-gray-900">{ebook.metadata.title}</span>
                {ebook.metadata.author && (
                  <span className="text-gray-500">by {ebook.metadata.author}</span>
                )}
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Badge variant="secondary" className="hidden sm:flex">
                {ebook.chapters.length} Chapters
              </Badge>
              <Badge variant="secondary" className="hidden sm:flex">
                {ebook.pages.length} Pages
              </Badge>
              <Button
                onClick={onDownload}
                className="bg-indigo-600 hover:bg-indigo-700 text-white"
              >
                <Download className="w-4 h-4 mr-2" />
                Download PDF
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Table of Contents Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-24">
              <CardContent className="p-4">
                <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  Table of Contents
                </h3>
                <div className="space-y-2">
                  {ebook.chapters.map((chapter, index) => (
                    <button
                      key={index}
                      onClick={() => goToPage(chapter.startPage)}
                      className={`w-full text-left p-2 rounded text-sm transition-colors ${
                        currentPage >= chapter.startPage && currentPage <= chapter.endPage
                          ? 'bg-indigo-100 text-indigo-700 font-medium'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      <div className="truncate">{chapter.title}</div>
                      <div className="text-xs text-gray-500">
                        Pages {chapter.startPage + 1}-{chapter.endPage + 1}
                      </div>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>Reading Progress</span>
                <span>{Math.round(progress)}% complete</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>

            {/* Page Content */}
            <Card className={`${themeStyles.bg} ${themeStyles.border} shadow-lg`}>
              <CardContent className="p-0">
                <div className="min-h-[800px] p-8 md:p-12">
                  {/* Page Header */}
                  <div className="flex justify-between items-center mb-8 pb-4 border-b border-gray-200">
                    <div className={`text-sm ${themeStyles.accent}`}>
                      {ebook.metadata.title}
                    </div>
                    <div className="text-sm text-gray-500">
                      Page {currentPage + 1} of {ebook.pages.length}
                    </div>
                  </div>

                  {/* Page Content */}
                  <div className={`${themeStyles.text} leading-relaxed`}>
                    {ebook.pages[currentPage]?.content?.split('\n').map((paragraph, index) => (
                      <div key={index} className="mb-4">
                        {paragraph.startsWith('# ') ? (
                          <h1 className={`text-3xl font-bold ${themeStyles.accent} mb-6 mt-8`}>
                            {paragraph.replace('# ', '')}
                          </h1>
                        ) : paragraph.startsWith('## ') ? (
                          <h2 className={`text-2xl font-semibold ${themeStyles.accent} mb-4 mt-6`}>
                            {paragraph.replace('## ', '')}
                          </h2>
                        ) : paragraph.startsWith('### ') ? (
                          <h3 className={`text-xl font-medium ${themeStyles.accent} mb-3 mt-4`}>
                            {paragraph.replace('### ', '')}
                          </h3>
                        ) : paragraph.trim() ? (
                          <p className="text-justify">{paragraph}</p>
                        ) : null}
                      </div>
                    ))}
                  </div>

                  {/* Page Footer */}
                  <div className="mt-12 pt-6 border-t border-gray-200 flex justify-between items-center text-sm text-gray-500">
                    <div>
                      {ebook.metadata.author && `© ${ebook.metadata.author}`}
                    </div>
                    <div className="flex items-center gap-4">
                      <span>Generated by AI Ebook Generator</span>
                      <span>{currentPage + 1}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Navigation */}
            <div className="flex items-center justify-between mt-6">
              <Button
                onClick={prevPage}
                disabled={currentPage === 0}
                variant="outline"
                className="flex items-center gap-2"
              >
                <ChevronLeft className="w-4 h-4" />
                Previous
              </Button>

              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Page</span>
                <select
                  value={currentPage}
                  onChange={(e) => setCurrentPage(Number(e.target.value))}
                  className="px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  {ebook.pages.map((_, index) => (
                    <option key={index} value={index}>
                      {index + 1}
                    </option>
                  ))}
                </select>
                <span className="text-sm text-gray-600">of {ebook.pages.length}</span>
              </div>

              <Button
                onClick={nextPage}
                disabled={currentPage === ebook.pages.length - 1}
                variant="outline"
                className="flex items-center gap-2"
              >
                Next
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EbookViewer;