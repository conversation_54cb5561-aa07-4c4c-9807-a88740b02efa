// Mock data generator for ebook functionality

export const generateMockEbook = (inputText, title, author, theme) => {
  // Simulate AI processing by splitting text into chapters and pages
  const paragraphs = inputText.split('\n').filter(p => p.trim());
  
  // Create mock chapters based on content length
  const chapters = generateMockChapters(paragraphs, title);
  
  // Create pages with proper formatting
  const pages = generateMockPages(paragraphs, chapters);
  
  // Generate table of contents
  const tableOfContents = chapters.map(chapter => ({
    title: chapter.title,
    page: chapter.startPage + 1
  }));

  return {
    metadata: {
      title,
      author: author || 'Anonymous',
      theme,
      generatedAt: new Date().toISOString(),
      totalPages: pages.length,
      totalChapters: chapters.length,
      wordCount: inputText.split(' ').length
    },
    chapters,
    pages,
    tableOfContents
  };
};

const generateMockChapters = (paragraphs, title) => {
  const chapters = [];
  const totalParagraphs = paragraphs.length;
  
  // If content is short, create fewer chapters
  const chapterCount = Math.min(Math.max(2, Math.ceil(totalParagraphs / 10)), 8);
  const parasPerChapter = Math.ceil(totalParagraphs / chapterCount);
  
  for (let i = 0; i < chapterCount; i++) {
    const startPara = i * parasPerChapter;
    const endPara = Math.min((i + 1) * parasPerChapter - 1, totalParagraphs - 1);
    
    // Generate chapter titles based on content or generic names
    const chapterTitle = generateChapterTitle(i + 1, paragraphs.slice(startPara, endPara + 1));
    
    chapters.push({
      id: i + 1,
      title: chapterTitle,
      startParagraph: startPara,
      endParagraph: endPara,
      startPage: Math.floor(startPara / 3), // Roughly 3 paragraphs per page
      endPage: Math.floor(endPara / 3)
    });
  }
  
  return chapters;
};

const generateChapterTitle = (chapterNum, paragraphs) => {
  // Try to extract a meaningful title from the first few words
  const firstParagraph = paragraphs[0] || '';
  const words = firstParagraph.split(' ').slice(0, 5);
  
  if (words.length >= 3) {
    return `Chapter ${chapterNum}: ${words.join(' ')}...`;
  }
  
  // Fallback to generic chapter names
  const genericTitles = [
    'Introduction',
    'Getting Started',
    'Core Concepts',
    'Advanced Topics',
    'Implementation',
    'Best Practices',
    'Case Studies',
    'Conclusion'
  ];
  
  return `Chapter ${chapterNum}: ${genericTitles[chapterNum - 1] || `Topic ${chapterNum}`}`;
};

const generateMockPages = (paragraphs, chapters) => {
  const pages = [];
  const wordsPerPage = 300; // Target words per page
  
  let currentPageContent = '';
  let currentWordCount = 0;
  let currentParagraphIndex = 0;
  
  for (const paragraph of paragraphs) {
    const paragraphWords = paragraph.split(' ').length;
    
    // Check if adding this paragraph would exceed page limit
    if (currentWordCount + paragraphWords > wordsPerPage && currentPageContent.trim()) {
      // Create new page
      pages.push({
        pageNumber: pages.length + 1,
        content: currentPageContent.trim(),
        wordCount: currentWordCount,
        chapter: findChapterForParagraph(currentParagraphIndex, chapters)
      });
      
      // Start new page
      currentPageContent = paragraph + '\n\n';
      currentWordCount = paragraphWords;
    } else {
      // Add to current page
      currentPageContent += paragraph + '\n\n';
      currentWordCount += paragraphWords;
    }
    
    currentParagraphIndex++;
  }
  
  // Add final page if there's remaining content
  if (currentPageContent.trim()) {
    pages.push({
      pageNumber: pages.length + 1,
      content: currentPageContent.trim(),
      wordCount: currentWordCount,
      chapter: findChapterForParagraph(currentParagraphIndex - 1, chapters)
    });
  }
  
  // Update chapter page ranges based on actual pages
  updateChapterPageRanges(chapters, pages);
  
  return pages;
};

const findChapterForParagraph = (paragraphIndex, chapters) => {
  for (const chapter of chapters) {
    if (paragraphIndex >= chapter.startParagraph && paragraphIndex <= chapter.endParagraph) {
      return chapter.id;
    }
  }
  return 1; // Default to first chapter
};

const updateChapterPageRanges = (chapters, pages) => {
  chapters.forEach(chapter => {
    const chapterPages = pages.filter(page => page.chapter === chapter.id);
    if (chapterPages.length > 0) {
      chapter.startPage = chapterPages[0].pageNumber - 1; // 0-indexed
      chapter.endPage = chapterPages[chapterPages.length - 1].pageNumber - 1; // 0-indexed
    }
  });
};

// Sample content for demonstration
export const sampleContent = `Welcome to the Future of Technology

The rapid advancement of technology has transformed our world in unprecedented ways. From artificial intelligence to quantum computing, we are witnessing a revolution that touches every aspect of human life.

Understanding Artificial Intelligence

Artificial Intelligence represents one of the most significant technological breakthroughs of our time. It encompasses machine learning, natural language processing, and computer vision, enabling machines to perform tasks that traditionally required human intelligence.

Machine learning algorithms can analyze vast amounts of data to identify patterns and make predictions. This capability has revolutionized industries from healthcare to finance, enabling more accurate diagnoses and better investment decisions.

Natural language processing allows computers to understand and generate human language. This technology powers virtual assistants, translation services, and content generation tools that make our daily lives more convenient.

Computer vision enables machines to interpret and analyze visual information. From autonomous vehicles to medical imaging, this technology is creating new possibilities for automation and precision.

The Impact on Society

The integration of AI into various sectors has brought both opportunities and challenges. While it has increased efficiency and opened new possibilities, it has also raised concerns about job displacement and privacy.

Healthcare has seen remarkable improvements through AI-powered diagnostic tools and personalized treatment plans. Doctors can now detect diseases earlier and provide more targeted therapies.

Education is being transformed through personalized learning platforms that adapt to individual student needs. AI tutors can provide instant feedback and customize lesson plans for optimal learning outcomes.

Transportation is evolving with autonomous vehicles and smart traffic management systems. These innovations promise to reduce accidents and improve traffic flow in urban areas.

Looking Ahead

As we advance into the future, the responsible development and deployment of AI will be crucial. We must balance innovation with ethical considerations to ensure that technology serves humanity's best interests.

The collaboration between humans and machines will define the next era of progress. By embracing these changes thoughtfully, we can create a future where technology enhances human potential rather than replacing it.

Conclusion

The journey of technological advancement continues to unfold with exciting possibilities. By staying informed and engaged, we can help shape a future where innovation benefits everyone and contributes to a better world for all.`;

export default {
  generateMockEbook,
  sampleContent
};