from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid
from enum import Enum

class ThemeType(str, Enum):
    MODERN = "modern"
    CLASSIC = "classic"
    ELEGANT = "elegant"
    CREATIVE = "creative"
    ACADEMIC = "academic"

class HierarchyLevel(str, Enum):
    H1 = "h1"  # Book Title
    H2 = "h2"  # Chapter Title
    H3 = "h3"  # Major Section
    H4 = "h4"  # Subsection
    H5 = "h5"  # Category/Process Step
    H6 = "h6"  # Implementation Detail

class PageBreakPriority(str, Enum):
    MANDATORY = "mandatory"
    PREFERRED = "preferred"
    ALLOWED = "allowed"
    AVOIDED = "avoided"

class ContentElement(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    type: str  # "heading", "paragraph", "list", "table", "image"
    level: Optional[HierarchyLevel] = None
    content: str
    word_count: int = 0
    break_priority: Optional[PageBreakPriority] = None
    page_break_before: bool = False
    page_break_after: bool = False
    
    def dict(self, *args, **kwargs):
        """Override dict method to handle enum serialization"""
        data = super().dict(*args, **kwargs)
        if self.level:
            data['level'] = self.level.value
        if self.break_priority:
            data['break_priority'] = self.break_priority.value
        return data

class Chapter(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str
    start_page: int = 0
    end_page: int = 0
    start_element: int = 0
    end_element: int = 0
    word_count: int = 0
    elements: List[ContentElement] = []

class Page(BaseModel):
    page_number: int
    content: str
    word_count: int = 0
    elements: List[ContentElement] = []
    chapter_id: Optional[str] = None
    has_orphan: bool = False
    has_widow: bool = False

class TableOfContents(BaseModel):
    entries: List[Dict[str, Any]] = []

class EbookMetadata(BaseModel):
    title: str
    author: Optional[str] = None
    theme: ThemeType
    total_pages: int = 0
    total_chapters: int = 0
    word_count: int = 0
    generated_at: datetime = Field(default_factory=datetime.utcnow)
    processing_time: Optional[float] = None

class EbookRequest(BaseModel):
    title: str
    author: Optional[str] = None
    content: str
    theme: ThemeType = ThemeType.MODERN
    max_words_per_page: int = 300
    min_words_per_page: int = 50

class EbookResponse(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    metadata: EbookMetadata
    chapters: List[Chapter]
    pages: List[Page]
    table_of_contents: TableOfContents
    formatted_content: List[ContentElement]
    processing_log: List[str] = []

class EbookGeneration(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    request: EbookRequest
    response: Optional[EbookResponse] = None
    status: str = "pending"  # pending, processing, completed, failed
    error_message: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None