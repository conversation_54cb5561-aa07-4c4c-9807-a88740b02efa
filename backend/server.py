import sys
import os
from pathlib import Path

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from fastapi import FastAPI, APIRouter, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.cors import CORSMiddleware
import logging
from dotenv import load_dotenv
from models.ebook import EbookRequest

# Load environment variables
load_dotenv()

# Create the main app without a prefix
app = FastAPI(title="AI Ebook Generator API", version="1.0.0")

# Create a router with the /api prefix
api_router = APIRouter(prefix="/api")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Basic health check
@api_router.get("/")
async def root():
    return {"message": "AI Ebook Generator API is running"}

@api_router.post("/test-gemini")
async def test_gemini_connection():
    """
    Test Gemini AI connection
    """
    try:
        from services.gemini_service import GeminiEbookService
        
        # Check if API key is available
        api_key = os.environ.get('GEMINI_API_KEY')
        if not api_key:
            return {
                "status": "error",
                "message": "GEMINI_API_KEY environment variable not found. Please set it in your .env file."
            }
        
        # Test with a simple content
        gemini_service = GeminiEbookService()
        test_content = "This is a test paragraph to verify Gemini AI integration."
        test_title = "Test Ebook"
        
        elements = await gemini_service.analyze_and_structure_content(test_content, test_title)
        
        return {
            "status": "success",
            "message": "Gemini connection successful",
            "elements_generated": len(elements),
            "sample_element": {
                "type": elements[0].type if elements else "paragraph",
                "content": elements[0].content if elements else "Test content"
            }
        }
    except Exception as e:
        logger.error(f"Gemini connection test failed: {str(e)}")
        return {
            "status": "error",
            "message": f"Gemini connection failed: {str(e)}"
        }

@api_router.post("/generate-ebook")
async def generate_ebook(request: EbookRequest):
    """
    Generate an AI-powered ebook with sophisticated formatting and intelligent page breaks
    """
    try:
        from services.ebook_service import EbookGenerationService
        import time
        
        # Check if API key is available
        api_key = os.environ.get('GEMINI_API_KEY')
        if not api_key:
            raise HTTPException(
                status_code=500, 
                detail="GEMINI_API_KEY environment variable not found. Please set it in your .env file."
            )
        
        # Generate ebook using the service
        ebook_service = EbookGenerationService()
        ebook_response = await ebook_service.generate_ebook(request)
        
        # Convert to dict for JSON response
        return {
            "id": ebook_response.id,
            "metadata": {
                "title": ebook_response.metadata.title,
                "author": ebook_response.metadata.author,
                "theme": ebook_response.metadata.theme,
                "total_pages": ebook_response.metadata.total_pages,
                "total_chapters": ebook_response.metadata.total_chapters,
                "word_count": ebook_response.metadata.word_count,
                "generated_at": ebook_response.metadata.generated_at,
                "processing_time": ebook_response.metadata.processing_time
            },
            "chapters": [
                {
                    "id": chapter.id,
                    "title": chapter.title,
                    "start_page": chapter.start_page,
                    "end_page": chapter.end_page,
                    "word_count": chapter.word_count,
                    "elements": [elem.dict() for elem in chapter.elements]
                }
                for chapter in ebook_response.chapters
            ],
            "pages": [
                {
                    "page_number": page.page_number,
                    "content": page.content,
                    "word_count": page.word_count,
                    "elements": [elem.dict() for elem in page.elements]
                }
                for page in ebook_response.pages
            ],
            "table_of_contents": {
                "entries": [
                    {
                        "title": entry.title,
                        "page": entry.page,
                        "level": entry.level,
                        "type": entry.type
                    }
                    for entry in ebook_response.table_of_contents.entries
                ]
            },
            "formatted_content": [
                {
                    "id": elem.id,
                    "type": elem.type,
                    "level": elem.level.value if elem.level else None,
                    "content": elem.content,
                    "word_count": elem.word_count
                }
                for elem in ebook_response.formatted_content
            ],
            "processing_log": ebook_response.processing_log
        }
    except Exception as e:
        logger.error(f"Error during ebook generation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Ebook generation failed: {str(e)}")

@api_router.get("/generation/{generation_id}")
async def get_generation_status(generation_id: str):
    """
    Get the status of an ebook generation
    """
    try:
        # Simulate getting generation status
        return {
            "id": generation_id,
            "status": "completed",
            "created_at": "2023-07-04T12:00:00Z",
            "completed_at": "2023-07-04T12:01:00Z",
            "title": "Test Ebook"
        }
    except Exception as e:
        logger.error(f"Error getting generation status: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api_router.get("/generations")
async def list_generations(limit: int = 10, offset: int = 0):
    """
    List recent ebook generations
    """
    try:
        # Simulate listing generations
        return {
            "generations": [
                {
                    "id": "test-generation-id-1",
                    "status": "completed",
                    "title": "Test Ebook 1",
                    "author": "Test Author",
                    "created_at": "2023-07-04T12:00:00Z",
                    "completed_at": "2023-07-04T12:01:00Z"
                },
                {
                    "id": "test-generation-id-2",
                    "status": "completed",
                    "title": "Test Ebook 2",
                    "author": "Test Author",
                    "created_at": "2023-07-04T13:00:00Z",
                    "completed_at": "2023-07-04T13:01:00Z"
                }
            ]
        }
    except Exception as e:
        logger.error(f"Error listing generations: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Include the router in the main app
app.include_router(api_router)

app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)