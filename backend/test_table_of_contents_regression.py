#!/usr/bin/env python3
"""
Regression test for the 'dict' object has no attribute 'title' error.

This test specifically checks that the table of contents entries are properly
handled as dictionaries in the API response serialization.
"""

import asyncio
import os
from dotenv import load_dotenv
from models.ebook import EbookRequest, ThemeType
from services.ebook_service import EbookGenerationService

# Load environment variables
load_dotenv()

async def test_table_of_contents_structure():
    """Test that table of contents entries are dictionaries with correct structure"""
    
    # Check if API key is available
    api_key = os.environ.get('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY environment variable not found")
        return False
    
    try:
        # Create a test request
        request = EbookRequest(
            title="TOC Regression Test",
            author="Test Author",
            content="""
            Chapter 1: First Chapter
            This is the content of the first chapter.
            
            Chapter 2: Second Chapter
            This is the content of the second chapter.
            
            ### Section 2.1
            This is a subsection.
            
            #### Subsection 2.1.1
            This is a sub-subsection.
            """,
            theme=ThemeType.MODERN,
            max_words_per_page=300
        )
        
        # Generate ebook
        service = EbookGenerationService()
        result = await service.generate_ebook(request)
        
        # Test 1: Verify table of contents exists
        assert hasattr(result, 'table_of_contents'), "Result should have table_of_contents"
        assert hasattr(result.table_of_contents, 'entries'), "TOC should have entries"
        
        # Test 2: Verify entries are dictionaries (not objects)
        entries = result.table_of_contents.entries
        assert isinstance(entries, list), "TOC entries should be a list"
        assert len(entries) > 0, "TOC should have at least one entry"
        
        for i, entry in enumerate(entries):
            # This is the critical test - entries should be dictionaries
            assert isinstance(entry, dict), f"Entry {i} should be a dict, got {type(entry)}"
            
            # Test required keys exist
            required_keys = ['title', 'page', 'level', 'type']
            for key in required_keys:
                assert key in entry, f"Entry {i} should have key '{key}'"
            
            # Test that we can access values using dictionary syntax
            title = entry['title']  # This should work
            page = entry['page']    # This should work
            level = entry['level']  # This should work
            entry_type = entry['type']  # This should work
            
            # Verify types
            assert isinstance(title, str), f"Entry {i} title should be string"
            assert isinstance(page, int), f"Entry {i} page should be int"
            assert isinstance(level, int), f"Entry {i} level should be int"
            assert isinstance(entry_type, str), f"Entry {i} type should be string"
            
            print(f"✅ Entry {i}: {title} (page {page}, level {level}, type {entry_type})")
        
        # Test 3: Simulate the server.py serialization logic
        # This is what was failing before the fix
        try:
            serialized_entries = [
                {
                    "title": entry["title"],      # Dictionary access - should work
                    "page": entry["page"],        # Dictionary access - should work
                    "level": entry["level"],      # Dictionary access - should work
                    "type": entry["type"]         # Dictionary access - should work
                }
                for entry in entries
            ]
            print(f"✅ Serialization test passed - created {len(serialized_entries)} serialized entries")
        except Exception as e:
            print(f"❌ Serialization test failed: {str(e)}")
            return False
        
        # Test 4: Verify that object access would fail (this was the original bug)
        try:
            # This should fail because entries are dictionaries, not objects
            first_entry = entries[0]
            _ = first_entry.title  # This should raise AttributeError
            print("❌ Object access test failed - this should have raised an error")
            return False
        except AttributeError:
            print("✅ Object access correctly raises AttributeError (expected behavior)")
        
        print(f"✅ All table of contents regression tests passed!")
        print(f"📋 Verified {len(entries)} TOC entries are properly structured as dictionaries")
        return True
        
    except Exception as e:
        print(f"❌ Table of contents regression test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the regression test"""
    print("🔍 Running table of contents regression test...")
    print("This test verifies the fix for: 'dict' object has no attribute 'title'\n")
    
    success = await test_table_of_contents_structure()
    
    if success:
        print("\n🎉 Regression test PASSED - the bug is fixed!")
    else:
        print("\n💥 Regression test FAILED - the bug may still exist!")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
