import asyncio
import time
from typing import List, Dict, Any, Optional, Tuple
from models.ebook import (
    EbookRequest, EbookResponse, EbookMetadata, ContentElement,
    Chapter, Page, TableOfContents, HierarchyLevel, PageBreakPriority,
    ContentType, ContentDensityFactor
)
from services.gemini_service import GeminiEbookService
import logging

logger = logging.getLogger(__name__)

class EbookGenerationService:
    def __init__(self):
        self.gemini_service = GeminiEbookService()
        self.max_words_per_page = 300
        self.min_words_per_page = 50
        self.orphan_widow_threshold = 3  # Minimum lines to prevent orphans/widows
        self.density_factors = ContentDensityFactor()

        # Page capacity allocation (following instruction guidelines)
        self.text_content_ratio = 0.80  # 80% of page for text content
        self.heading_ratio = 0.15       # 15% of page for headings
        self.whitespace_ratio = 0.05    # 5% of page for whitespace/margins
    
    async def generate_ebook(self, request: EbookRequest) -> EbookResponse:
        """Main method to generate a complete ebook with sophisticated formatting"""
        start_time = time.time()
        processing_log = []
        
        try:
            # Step 1: Analyze and structure content with Gemini
            processing_log.append("Starting content analysis with Gemini AI...")
            structured_elements = await self.gemini_service.analyze_and_structure_content(
                request.content, request.title
            )
            processing_log.append(f"Content structured into {len(structured_elements)} elements")
            
            # Step 2: Create chapters from structured content
            processing_log.append("Creating chapter structure...")
            chapters = await self._create_chapters(structured_elements)
            processing_log.append(f"Created {len(chapters)} chapters")
            
            # Step 3: Generate pages with intelligent page breaks
            processing_log.append("Generating pages with intelligent formatting...")
            pages = await self._generate_pages(
                structured_elements, 
                chapters, 
                request.max_words_per_page or self.max_words_per_page
            )
            processing_log.append(f"Generated {len(pages)} pages")
            
            # Step 4: Apply orphan/widow prevention
            processing_log.append("Applying orphan/widow prevention...")
            pages = await self._apply_orphan_widow_prevention(pages, structured_elements)
            
            # Step 5: Generate table of contents
            processing_log.append("Generating table of contents...")
            toc = await self._generate_table_of_contents(chapters, pages)

            # Step 6: Quality assurance validation
            processing_log.append("Performing quality assurance validation...")
            validation_results = await self._perform_quality_assurance(pages, chapters, toc)
            processing_log.extend(validation_results)

            # Step 7: Create metadata
            processing_time = time.time() - start_time
            metadata = EbookMetadata(
                title=request.title,
                author=request.author,
                theme=request.theme,
                total_pages=len(pages),
                total_chapters=len(chapters),
                word_count=sum(elem.word_count for elem in structured_elements),
                processing_time=processing_time
            )
            
            processing_log.append(f"Ebook generation completed in {processing_time:.2f} seconds")
            
            return EbookResponse(
                metadata=metadata,
                chapters=chapters,
                pages=pages,
                table_of_contents=toc,
                formatted_content=structured_elements,
                processing_log=processing_log
            )
            
        except Exception as e:
            logger.error(f"Error in ebook generation: {str(e)}")
            raise
    
    async def _create_chapters(self, elements: List[ContentElement]) -> List[Chapter]:
        """Create chapter structure from content elements"""
        chapters = []
        current_chapter = None
        current_chapter_elements = []
        
        for i, element in enumerate(elements):
            # H2 elements mark chapter boundaries
            if element.level == HierarchyLevel.H2:
                # Save previous chapter if exists
                if current_chapter:
                    current_chapter.elements = current_chapter_elements
                    current_chapter.word_count = sum(elem.word_count for elem in current_chapter_elements)
                    current_chapter.end_element = i - 1
                    chapters.append(current_chapter)
                
                # Start new chapter
                current_chapter = Chapter(
                    title=element.content,
                    start_element=i,
                    elements=[]
                )
                current_chapter_elements = [element]
                
            elif current_chapter:
                current_chapter_elements.append(element)
            else:
                # Handle content before first chapter
                if not chapters:
                    # Create default first chapter
                    current_chapter = Chapter(
                        title="Introduction",
                        start_element=0,
                        elements=[]
                    )
                    current_chapter_elements = []
                
                current_chapter_elements.append(element)
        
        # Add final chapter
        if current_chapter:
            current_chapter.elements = current_chapter_elements
            current_chapter.word_count = sum(elem.word_count for elem in current_chapter_elements)
            current_chapter.end_element = len(elements) - 1
            chapters.append(current_chapter)
        
        return chapters
    
    async def _generate_pages(self, elements: List[ContentElement], chapters: List[Chapter], max_words_per_page: int) -> List[Page]:
        """Generate pages with intelligent page breaks following the sophisticated rules"""
        pages = []
        current_page_elements = []
        current_page_word_count = 0
        page_number = 1

        i = 0
        while i < len(elements):
            element = elements[i]

            # Check for mandatory page breaks
            if element.page_break_before or element.break_priority == PageBreakPriority.MANDATORY:
                if current_page_elements:
                    # Save current page
                    page_content = self._elements_to_content(current_page_elements)
                    chapter_id = self._find_chapter_for_elements(current_page_elements, chapters)

                    page = Page(
                        page_number=page_number,
                        content=page_content,
                        word_count=current_page_word_count,
                        elements=current_page_elements.copy(),
                        chapter_id=chapter_id
                    )
                    pages.append(page)

                    # Reset for new page
                    current_page_elements = []
                    current_page_word_count = 0
                    page_number += 1

            # Calculate effective word count considering density
            effective_word_count = element.calculate_effective_word_count()

            # Calculate dynamic page capacity based on current content
            current_capacity = self._calculate_page_capacity(current_page_elements + [element])

            # Check if adding this element would exceed page capacity
            if (current_page_word_count + effective_word_count > current_capacity and
                current_page_elements):

                # Apply section completeness rules
                section_elements = await self._get_complete_section(elements, i)
                section_word_count = sum(elem.calculate_effective_word_count() for elem in section_elements)

                # Primary Rule: A section should never be split across pages unless it exceeds the maximum page capacity
                if section_word_count <= current_capacity:
                    # Move entire section to next page
                    if current_page_elements:
                        page_content = self._elements_to_content(current_page_elements)
                        chapter_id = self._find_chapter_for_elements(current_page_elements, chapters)

                        page = Page(
                            page_number=page_number,
                            content=page_content,
                            word_count=current_page_word_count,
                            elements=current_page_elements.copy(),
                            chapter_id=chapter_id
                        )
                        pages.append(page)

                        # Reset for new page
                        current_page_elements = []
                        current_page_word_count = 0
                        page_number += 1
                else:
                    # Section exceeds page capacity - need to split at natural break points
                    split_elements = await self._split_section_at_natural_breaks(section_elements, current_capacity)

                    # Add first part to current page if it fits
                    if split_elements and current_page_word_count + sum(elem.calculate_effective_word_count() for elem in split_elements[0]) <= max_words_per_page:
                        current_page_elements.extend(split_elements[0])
                        current_page_word_count += sum(elem.calculate_effective_word_count() for elem in split_elements[0])

                        # Save current page
                        page_content = self._elements_to_content(current_page_elements)
                        chapter_id = self._find_chapter_for_elements(current_page_elements, chapters)

                        page = Page(
                            page_number=page_number,
                            content=page_content,
                            word_count=current_page_word_count,
                            elements=current_page_elements.copy(),
                            chapter_id=chapter_id
                        )
                        pages.append(page)

                        # Reset for new page
                        current_page_elements = []
                        current_page_word_count = 0
                        page_number += 1

                        # Skip processed elements
                        i += len(split_elements[0])
                        continue
                    else:
                        # Start new page with current element
                        if current_page_elements:
                            page_content = self._elements_to_content(current_page_elements)
                            chapter_id = self._find_chapter_for_elements(current_page_elements, chapters)

                            page = Page(
                                page_number=page_number,
                                content=page_content,
                                word_count=current_page_word_count,
                                elements=current_page_elements.copy(),
                                chapter_id=chapter_id
                            )
                            pages.append(page)

                            # Reset for new page
                            current_page_elements = []
                            current_page_word_count = 0
                            page_number += 1

            # Add element to current page
            current_page_elements.append(element)
            current_page_word_count += effective_word_count

            # Check for page break after element
            if element.page_break_after:
                page_content = self._elements_to_content(current_page_elements)
                chapter_id = self._find_chapter_for_elements(current_page_elements, chapters)

                page = Page(
                    page_number=page_number,
                    content=page_content,
                    word_count=current_page_word_count,
                    elements=current_page_elements.copy(),
                    chapter_id=chapter_id
                )
                pages.append(page)

                # Reset for new page
                current_page_elements = []
                current_page_word_count = 0
                page_number += 1

            i += 1

        # Add final page if there are remaining elements
        if current_page_elements:
            page_content = self._elements_to_content(current_page_elements)
            chapter_id = self._find_chapter_for_elements(current_page_elements, chapters)

            page = Page(
                page_number=page_number,
                content=page_content,
                word_count=current_page_word_count,
                elements=current_page_elements.copy(),
                chapter_id=chapter_id
            )
            pages.append(page)

        # Update chapter page ranges
        await self._update_chapter_page_ranges(chapters, pages)

        return pages
    
    async def _should_break_page(self, element: ContentElement, current_page_elements: List[ContentElement], max_words_per_page: int) -> bool:
        """Determine if a page break should occur based on sophisticated rules"""
        
        # Never break within avoided priority content
        if element.break_priority == PageBreakPriority.AVOIDED:
            return False
        
        # Always break for mandatory priority
        if element.break_priority == PageBreakPriority.MANDATORY:
            return True
        
        # Check section completeness - if element is part of a larger section
        if element.level in [HierarchyLevel.H5, HierarchyLevel.H6]:
            # Keep H5/H6 with their parent sections
            return False
        
        # Check if we're over 80% of page capacity
        current_word_count = sum(elem.word_count for elem in current_page_elements)
        if current_word_count / max_words_per_page > 0.8:
            return True
        
        # For preferred breaks, break if we're over 70% capacity
        if element.break_priority == PageBreakPriority.PREFERRED:
            if current_word_count / max_words_per_page > 0.7:
                return True
        
        return False

    async def _get_complete_section(self, elements: List[ContentElement], start_index: int) -> List[ContentElement]:
        """Get all elements that belong to the same logical section"""
        section_elements = []
        current_element = elements[start_index]
        section_elements.append(current_element)

        # If this is a heading, include all content until the next heading of same or higher level
        if current_element.level:
            current_level = current_element.level

            for i in range(start_index + 1, len(elements)):
                next_element = elements[i]

                # Stop if we hit a heading of same or higher level
                if (next_element.level and
                    self._get_hierarchy_order(next_element.level) <= self._get_hierarchy_order(current_level)):
                    break

                section_elements.append(next_element)
        else:
            # For non-heading elements, just return the single element
            pass

        return section_elements

    def _get_hierarchy_order(self, level: HierarchyLevel) -> int:
        """Get numeric order for hierarchy levels (lower number = higher level)"""
        order_map = {
            HierarchyLevel.H1: 1,
            HierarchyLevel.H2: 2,
            HierarchyLevel.H3: 3,
            HierarchyLevel.H4: 4,
            HierarchyLevel.H5: 5,
            HierarchyLevel.H6: 6,
        }
        return order_map.get(level, 7)

    async def _split_section_at_natural_breaks(self, section_elements: List[ContentElement], max_words_per_page: int) -> List[List[ContentElement]]:
        """Split a section at natural break points when it exceeds page capacity"""
        if not section_elements:
            return []

        # Find natural break points
        break_points = await self._find_natural_break_points(section_elements)

        # Split into chunks that fit within page capacity
        chunks = []
        current_chunk = []
        current_word_count = 0

        for i, element in enumerate(section_elements):
            effective_word_count = element.calculate_effective_word_count()

            # Check if adding this element would exceed capacity
            if (current_word_count + effective_word_count > max_words_per_page and
                current_chunk and i in break_points):

                # Save current chunk
                chunks.append(current_chunk)
                current_chunk = [element]
                current_word_count = effective_word_count
            else:
                current_chunk.append(element)
                current_word_count += effective_word_count

        # Add final chunk
        if current_chunk:
            chunks.append(current_chunk)

        return chunks

    async def _find_natural_break_points(self, elements: List[ContentElement]) -> List[int]:
        """Find natural break points within a section"""
        break_points = []

        for i, element in enumerate(elements):
            # Break points at paragraph boundaries
            if element.type == ContentType.PARAGRAPH and i > 0:
                break_points.append(i)

            # Break points before subsections (H4, H5, H6)
            if element.level in [HierarchyLevel.H4, HierarchyLevel.H5, HierarchyLevel.H6]:
                break_points.append(i)

            # Break points before lists and tables
            if element.type in [ContentType.LIST, ContentType.TABLE, ContentType.CHECKLIST]:
                break_points.append(i)

        return break_points

    def _calculate_page_capacity(self, elements: List[ContentElement]) -> int:
        """Calculate page capacity based on content type distribution and guidelines"""
        if not elements:
            return self.max_words_per_page

        # Analyze content type distribution
        heading_count = sum(1 for elem in elements if elem.level)
        text_count = len(elements) - heading_count

        # Calculate capacity based on 80% text, 15% headings, 5% whitespace
        base_capacity = self.max_words_per_page

        # Adjust for heading ratio
        heading_ratio = heading_count / len(elements) if elements else 0
        if heading_ratio > self.heading_ratio:
            # More headings than expected - reduce capacity
            adjustment = (heading_ratio - self.heading_ratio) * 0.5
            base_capacity = int(base_capacity * (1 - adjustment))

        # Apply content density factors
        total_density_factor = 0
        for element in elements:
            total_density_factor += element.get_density_factor()

        avg_density_factor = total_density_factor / len(elements) if elements else 1.0

        # Adjust capacity based on average density
        adjusted_capacity = int(base_capacity / avg_density_factor)

        # Ensure capacity stays within reasonable bounds
        min_capacity = int(self.max_words_per_page * 0.6)  # At least 60% of base
        max_capacity = int(self.max_words_per_page * 1.4)  # At most 140% of base

        return max(min_capacity, min(adjusted_capacity, max_capacity))

    def _calculate_effective_page_metrics(self, page: Page) -> dict:
        """Calculate effective page metrics following the 80/15/5 guideline"""
        metrics = {
            'total_words': page.word_count,
            'text_words': 0,
            'heading_words': 0,
            'whitespace_estimate': 0,
            'text_ratio': 0,
            'heading_ratio': 0,
            'whitespace_ratio': 0,
            'follows_guidelines': False
        }

        # Calculate word distribution
        for element in page.elements:
            if element.level:  # Heading
                metrics['heading_words'] += element.word_count
            else:  # Text content
                metrics['text_words'] += element.word_count

        # Estimate whitespace (5% of total capacity)
        metrics['whitespace_estimate'] = int(self.max_words_per_page * self.whitespace_ratio)

        # Calculate ratios
        if page.word_count > 0:
            metrics['text_ratio'] = metrics['text_words'] / page.word_count
            metrics['heading_ratio'] = metrics['heading_words'] / page.word_count
            metrics['whitespace_ratio'] = metrics['whitespace_estimate'] / page.word_count

        # Check if follows guidelines (with 10% tolerance)
        text_target = self.text_content_ratio
        heading_target = self.heading_ratio

        metrics['follows_guidelines'] = (
            abs(metrics['text_ratio'] - text_target) <= 0.1 and
            abs(metrics['heading_ratio'] - heading_target) <= 0.1
        )

        return metrics
    
    async def _apply_orphan_widow_prevention(self, pages: List[Page], elements: List[ContentElement]) -> List[Page]:
        """Apply advanced orphan and widow prevention rules"""
        if len(pages) < 2:
            return pages

        adjusted_pages = []
        i = 0

        while i < len(pages):
            current_page = pages[i]

            # Check for orphans (minimum 3 lines before page break)
            if i < len(pages) - 1:  # Not the last page
                next_page = pages[i + 1]

                # Estimate lines for last element of current page
                last_element = current_page.elements[-1] if current_page.elements else None
                if last_element:
                    estimated_lines = self._estimate_lines(last_element)

                    # If last element has fewer than 3 lines, move it to next page
                    if estimated_lines < self.orphan_widow_threshold:
                        # Remove last element from current page
                        orphan_element = current_page.elements.pop()
                        current_page.word_count -= orphan_element.calculate_effective_word_count()
                        current_page.content = self._elements_to_content(current_page.elements)

                        # Add to next page at the beginning
                        next_page.elements.insert(0, orphan_element)
                        next_page.word_count += orphan_element.calculate_effective_word_count()
                        next_page.content = self._elements_to_content(next_page.elements)

                        current_page.has_orphan = False

            # Check for widows (minimum 3 lines after page break)
            if i > 0:  # Not the first page
                # Estimate lines for first element of current page
                first_element = current_page.elements[0] if current_page.elements else None
                if first_element:
                    estimated_lines = self._estimate_lines(first_element)

                    # If first element has fewer than 3 lines, move it to previous page
                    if estimated_lines < self.orphan_widow_threshold and len(adjusted_pages) > 0:
                        previous_page = adjusted_pages[-1]

                        # Check if previous page has room
                        if (previous_page.word_count + first_element.calculate_effective_word_count()
                            <= self.max_words_per_page * 1.1):  # Allow 10% overflow for widow prevention

                            # Remove first element from current page
                            widow_element = current_page.elements.pop(0)
                            current_page.word_count -= widow_element.calculate_effective_word_count()
                            current_page.content = self._elements_to_content(current_page.elements)

                            # Add to previous page at the end
                            previous_page.elements.append(widow_element)
                            previous_page.word_count += widow_element.calculate_effective_word_count()
                            previous_page.content = self._elements_to_content(previous_page.elements)

                            current_page.has_widow = False

            # Check for heading orphans (headings with no following content)
            if current_page.elements:
                for j, element in enumerate(current_page.elements):
                    if (element.level and j == len(current_page.elements) - 1 and
                        i < len(pages) - 1):  # Heading at end of page

                        next_page = pages[i + 1]
                        if next_page.elements:
                            # Move heading to next page
                            heading_element = current_page.elements.pop()
                            current_page.word_count -= heading_element.calculate_effective_word_count()
                            current_page.content = self._elements_to_content(current_page.elements)

                            next_page.elements.insert(0, heading_element)
                            next_page.word_count += heading_element.calculate_effective_word_count()
                            next_page.content = self._elements_to_content(next_page.elements)

                            break

            adjusted_pages.append(current_page)
            i += 1

        return adjusted_pages

    def _estimate_lines(self, element: ContentElement) -> int:
        """Estimate number of lines for an element"""
        # Rough estimation: 10-12 words per line for standard text
        words_per_line = 10

        # Adjust for content type
        if element.type == ContentType.HEADING:
            return 1  # Headings are typically one line
        elif element.type in [ContentType.TABLE, ContentType.CODE_BLOCK]:
            words_per_line = 6  # Tables and code have fewer words per line
        elif element.type in [ContentType.LIST, ContentType.CHECKLIST]:
            words_per_line = 8  # Lists have fewer words per line

        estimated_lines = max(1, element.word_count // words_per_line)
        return estimated_lines
    
    async def _generate_table_of_contents(self, chapters: List[Chapter], pages: List[Page]) -> TableOfContents:
        """Generate table of contents with accurate page numbers"""
        toc_entries = []

        # Create a mapping of element IDs to page numbers
        element_to_page = {}
        for page in pages:
            for element in page.elements:
                element_to_page[element.id] = page.page_number

        # Process chapters and their headings
        for chapter in chapters:
            # Find the page number for this chapter
            chapter_page = chapter.start_page if chapter.start_page else 1

            toc_entries.append({
                "title": chapter.title,
                "page": chapter_page,
                "level": 1,
                "type": "chapter"
            })

            # Add sub-headings from the chapter
            for element in chapter.elements:
                if element.level and element.level != HierarchyLevel.H1:  # Skip main chapter headings
                    page_number = element_to_page.get(element.id, chapter_page)
                    level = self._get_toc_level(element.level)

                    toc_entries.append({
                        "title": element.content.strip(),
                        "page": page_number,
                        "level": level,
                        "type": "section"
                    })

        return TableOfContents(entries=toc_entries)

    def _get_toc_level(self, hierarchy_level: HierarchyLevel) -> int:
        """Convert hierarchy level to TOC indentation level"""
        level_map = {
            HierarchyLevel.H1: 1,
            HierarchyLevel.H2: 2,
            HierarchyLevel.H3: 3,
            HierarchyLevel.H4: 4,
            HierarchyLevel.H5: 5,
            HierarchyLevel.H6: 6,
        }
        return level_map.get(hierarchy_level, 2)

    async def _perform_quality_assurance(self, pages: List[Page], chapters: List[Chapter], toc: TableOfContents) -> List[str]:
        """Perform comprehensive quality assurance validation"""
        validation_results = []

        # 1. Structural Integrity Checks
        validation_results.extend(await self._validate_structural_integrity(pages, chapters))

        # 2. Content Completeness Checks
        validation_results.extend(await self._validate_content_completeness(pages, chapters))

        # 3. Readability Checks
        validation_results.extend(await self._validate_readability(pages))

        # 4. Page Distribution Checks
        validation_results.extend(await self._validate_page_distribution(pages))

        # 5. Table of Contents Validation
        validation_results.extend(await self._validate_table_of_contents(toc, pages))

        return validation_results

    async def _validate_structural_integrity(self, pages: List[Page], chapters: List[Chapter]) -> List[str]:
        """Validate structural integrity of the ebook"""
        issues = []

        # Check for empty pages
        empty_pages = [page.page_number for page in pages if not page.elements or page.word_count == 0]
        if empty_pages:
            issues.append(f"Warning: Found empty pages: {empty_pages}")

        # Check for orphaned headings
        for page in pages:
            for i, element in enumerate(page.elements):
                if (element.level and i == len(page.elements) - 1 and
                    page.page_number < len(pages)):  # Heading at end of page
                    issues.append(f"Warning: Orphaned heading on page {page.page_number}: '{element.content[:50]}...'")

        # Check chapter continuity
        for i, chapter in enumerate(chapters):
            if chapter.start_page and chapter.end_page:
                if chapter.start_page > chapter.end_page:
                    issues.append(f"Error: Chapter {i+1} has invalid page range: {chapter.start_page}-{chapter.end_page}")

        if not issues:
            issues.append("✓ Structural integrity validation passed")

        return issues

    async def _validate_content_completeness(self, pages: List[Page], chapters: List[Chapter]) -> List[str]:
        """Validate content completeness"""
        issues = []

        # Check for missing content
        total_elements = sum(len(page.elements) for page in pages)
        if total_elements == 0:
            issues.append("Error: No content elements found")

        # Check chapter content distribution
        for i, chapter in enumerate(chapters):
            if not chapter.elements:
                issues.append(f"Warning: Chapter {i+1} '{chapter.title}' has no content")
            elif len(chapter.elements) < 3:
                issues.append(f"Warning: Chapter {i+1} '{chapter.title}' has very little content ({len(chapter.elements)} elements)")

        # Check for content density balance
        total_word_count = sum(page.word_count for page in pages)
        avg_words_per_page = total_word_count / len(pages) if pages else 0

        if avg_words_per_page < self.min_words_per_page:
            issues.append(f"Warning: Average words per page ({avg_words_per_page:.1f}) is below minimum ({self.min_words_per_page})")
        elif avg_words_per_page > self.max_words_per_page:
            issues.append(f"Warning: Average words per page ({avg_words_per_page:.1f}) exceeds maximum ({self.max_words_per_page})")

        if not issues:
            issues.append("✓ Content completeness validation passed")

        return issues

    async def _validate_readability(self, pages: List[Page]) -> List[str]:
        """Validate readability aspects"""
        issues = []

        # Check for overly long pages
        long_pages = [page.page_number for page in pages if page.word_count > self.max_words_per_page * 1.2]
        if long_pages:
            issues.append(f"Warning: Pages exceed recommended length: {long_pages}")

        # Check for overly short pages (except last page)
        short_pages = [page.page_number for page in pages[:-1] if page.word_count < self.min_words_per_page]
        if short_pages:
            issues.append(f"Warning: Pages below minimum length: {short_pages}")

        # Check for proper heading hierarchy
        for page in pages:
            heading_levels = []
            for element in page.elements:
                if element.level:
                    level_num = self._get_hierarchy_order(element.level)
                    heading_levels.append(level_num)

            # Check for skipped heading levels
            if len(heading_levels) > 1:
                for i in range(1, len(heading_levels)):
                    if heading_levels[i] - heading_levels[i-1] > 1:
                        issues.append(f"Warning: Skipped heading level on page {page.page_number}")
                        break

        if not issues:
            issues.append("✓ Readability validation passed")

        return issues

    async def _validate_page_distribution(self, pages: List[Page]) -> List[str]:
        """Validate page content distribution"""
        issues = []

        # Check page capacity utilization
        underutilized_pages = []
        overutilized_pages = []

        for page in pages:
            utilization = page.word_count / self.max_words_per_page
            if utilization < 0.5 and page.page_number < len(pages):  # Not last page
                underutilized_pages.append(page.page_number)
            elif utilization > 1.1:
                overutilized_pages.append(page.page_number)

        if underutilized_pages:
            issues.append(f"Info: Underutilized pages (<50% capacity): {underutilized_pages}")
        if overutilized_pages:
            issues.append(f"Warning: Overutilized pages (>110% capacity): {overutilized_pages}")

        # Check content type distribution
        content_types = {}
        for page in pages:
            for element in page.elements:
                content_type = element.type.value if hasattr(element.type, 'value') else str(element.type)
                content_types[content_type] = content_types.get(content_type, 0) + 1

        if content_types:
            issues.append(f"Info: Content type distribution: {content_types}")

        if not issues:
            issues.append("✓ Page distribution validation passed")

        return issues

    async def _validate_table_of_contents(self, toc: TableOfContents, pages: List[Page]) -> List[str]:
        """Validate table of contents accuracy"""
        issues = []

        # Check TOC entries have valid page numbers
        max_page = len(pages)
        invalid_pages = []

        for entry in toc.entries:
            page_num = entry.get('page', 0)
            if page_num < 1 or page_num > max_page:
                invalid_pages.append(f"{entry.get('title', 'Unknown')} (page {page_num})")

        if invalid_pages:
            issues.append(f"Error: TOC entries with invalid page numbers: {invalid_pages}")

        # Check TOC completeness
        if len(toc.entries) == 0:
            issues.append("Warning: Table of contents is empty")
        elif len(toc.entries) < 3:
            issues.append("Warning: Table of contents has very few entries")

        if not issues:
            issues.append("✓ Table of contents validation passed")

        return issues
    
    def _elements_to_content(self, elements: List[ContentElement]) -> str:
        """Convert elements to formatted content string"""
        content_parts = []
        
        for element in elements:
            if element.type == "heading":
                if element.level == HierarchyLevel.H1:
                    content_parts.append(f"# {element.content}")
                elif element.level == HierarchyLevel.H2:
                    content_parts.append(f"## {element.content}")
                elif element.level == HierarchyLevel.H3:
                    content_parts.append(f"### {element.content}")
                elif element.level == HierarchyLevel.H4:
                    content_parts.append(f"#### {element.content}")
                elif element.level == HierarchyLevel.H5:
                    content_parts.append(f"##### {element.content}")
                elif element.level == HierarchyLevel.H6:
                    content_parts.append(f"###### {element.content}")
            else:
                content_parts.append(element.content)
        
        return "\n\n".join(content_parts)
    
    def _find_chapter_for_elements(self, elements: List[ContentElement], chapters: List[Chapter]) -> Optional[str]:
        """Find which chapter contains these elements"""
        if not elements:
            return None
        
        for chapter in chapters:
            if any(elem.id in [ch_elem.id for ch_elem in chapter.elements] for elem in elements):
                return chapter.id
        
        return None
    
    async def _update_chapter_page_ranges(self, chapters: List[Chapter], pages: List[Page]):
        """Update chapter page ranges based on actual page generation"""
        for chapter in chapters:
            chapter_pages = [page for page in pages if page.chapter_id == chapter.id]
            if chapter_pages:
                chapter.start_page = chapter_pages[0].page_number - 1  # 0-indexed
                chapter.end_page = chapter_pages[-1].page_number - 1  # 0-indexed