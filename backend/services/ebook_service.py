import asyncio
import time
from typing import List, Dict, Any, Optional, Tuple
from models.ebook import (
    EbookRequest, EbookResponse, EbookMetadata, ContentElement, 
    Chapter, Page, TableOfContents, HierarchyLevel, PageBreakPriority
)
from services.gemini_service import GeminiEbookService
import logging

logger = logging.getLogger(__name__)

class EbookGenerationService:
    def __init__(self):
        self.gemini_service = GeminiEbookService()
        self.max_words_per_page = 300
        self.min_words_per_page = 50
        self.orphan_widow_threshold = 3  # Minimum lines to prevent orphans/widows
    
    async def generate_ebook(self, request: EbookRequest) -> EbookResponse:
        """Main method to generate a complete ebook with sophisticated formatting"""
        start_time = time.time()
        processing_log = []
        
        try:
            # Step 1: Analyze and structure content with Gemini
            processing_log.append("Starting content analysis with Gemini AI...")
            structured_elements = await self.gemini_service.analyze_and_structure_content(
                request.content, request.title
            )
            processing_log.append(f"Content structured into {len(structured_elements)} elements")
            
            # Step 2: Create chapters from structured content
            processing_log.append("Creating chapter structure...")
            chapters = await self._create_chapters(structured_elements)
            processing_log.append(f"Created {len(chapters)} chapters")
            
            # Step 3: Generate pages with intelligent page breaks
            processing_log.append("Generating pages with intelligent formatting...")
            pages = await self._generate_pages(
                structured_elements, 
                chapters, 
                request.max_words_per_page or self.max_words_per_page
            )
            processing_log.append(f"Generated {len(pages)} pages")
            
            # Step 4: Apply orphan/widow prevention
            processing_log.append("Applying orphan/widow prevention...")
            pages = await self._apply_orphan_widow_prevention(pages, structured_elements)
            
            # Step 5: Generate table of contents
            processing_log.append("Generating table of contents...")
            toc = await self._generate_table_of_contents(chapters, structured_elements)
            
            # Step 6: Create metadata
            processing_time = time.time() - start_time
            metadata = EbookMetadata(
                title=request.title,
                author=request.author,
                theme=request.theme,
                total_pages=len(pages),
                total_chapters=len(chapters),
                word_count=sum(elem.word_count for elem in structured_elements),
                processing_time=processing_time
            )
            
            processing_log.append(f"Ebook generation completed in {processing_time:.2f} seconds")
            
            return EbookResponse(
                metadata=metadata,
                chapters=chapters,
                pages=pages,
                table_of_contents=toc,
                formatted_content=structured_elements,
                processing_log=processing_log
            )
            
        except Exception as e:
            logger.error(f"Error in ebook generation: {str(e)}")
            raise
    
    async def _create_chapters(self, elements: List[ContentElement]) -> List[Chapter]:
        """Create chapter structure from content elements"""
        chapters = []
        current_chapter = None
        current_chapter_elements = []
        
        for i, element in enumerate(elements):
            # H2 elements mark chapter boundaries
            if element.level == HierarchyLevel.H2:
                # Save previous chapter if exists
                if current_chapter:
                    current_chapter.elements = current_chapter_elements
                    current_chapter.word_count = sum(elem.word_count for elem in current_chapter_elements)
                    current_chapter.end_element = i - 1
                    chapters.append(current_chapter)
                
                # Start new chapter
                current_chapter = Chapter(
                    title=element.content,
                    start_element=i,
                    elements=[]
                )
                current_chapter_elements = [element]
                
            elif current_chapter:
                current_chapter_elements.append(element)
            else:
                # Handle content before first chapter
                if not chapters:
                    # Create default first chapter
                    current_chapter = Chapter(
                        title="Introduction",
                        start_element=0,
                        elements=[]
                    )
                    current_chapter_elements = []
                
                current_chapter_elements.append(element)
        
        # Add final chapter
        if current_chapter:
            current_chapter.elements = current_chapter_elements
            current_chapter.word_count = sum(elem.word_count for elem in current_chapter_elements)
            current_chapter.end_element = len(elements) - 1
            chapters.append(current_chapter)
        
        return chapters
    
    async def _generate_pages(self, elements: List[ContentElement], chapters: List[Chapter], max_words_per_page: int) -> List[Page]:
        """Generate pages with intelligent page breaks following the sophisticated rules"""
        pages = []
        current_page_elements = []
        current_page_word_count = 0
        page_number = 1
        
        for i, element in enumerate(elements):
            # Check for mandatory page breaks
            if element.page_break_before or element.break_priority == PageBreakPriority.MANDATORY:
                if current_page_elements:
                    # Save current page
                    page_content = self._elements_to_content(current_page_elements)
                    chapter_id = self._find_chapter_for_elements(current_page_elements, chapters)
                    
                    page = Page(
                        page_number=page_number,
                        content=page_content,
                        word_count=current_page_word_count,
                        elements=current_page_elements.copy(),
                        chapter_id=chapter_id
                    )
                    pages.append(page)
                    
                    # Reset for new page
                    current_page_elements = []
                    current_page_word_count = 0
                    page_number += 1
            
            # Check if adding this element would exceed page capacity
            if (current_page_word_count + element.word_count > max_words_per_page and 
                current_page_elements):
                
                # Apply section completeness rules
                if await self._should_break_page(element, current_page_elements, max_words_per_page):
                    # Save current page
                    page_content = self._elements_to_content(current_page_elements)
                    chapter_id = self._find_chapter_for_elements(current_page_elements, chapters)
                    
                    page = Page(
                        page_number=page_number,
                        content=page_content,
                        word_count=current_page_word_count,
                        elements=current_page_elements.copy(),
                        chapter_id=chapter_id
                    )
                    pages.append(page)
                    
                    # Reset for new page
                    current_page_elements = []
                    current_page_word_count = 0
                    page_number += 1
            
            # Add element to current page
            current_page_elements.append(element)
            current_page_word_count += element.word_count
            
            # Check for page break after element
            if element.page_break_after:
                page_content = self._elements_to_content(current_page_elements)
                chapter_id = self._find_chapter_for_elements(current_page_elements, chapters)
                
                page = Page(
                    page_number=page_number,
                    content=page_content,
                    word_count=current_page_word_count,
                    elements=current_page_elements.copy(),
                    chapter_id=chapter_id
                )
                pages.append(page)
                
                # Reset for new page
                current_page_elements = []
                current_page_word_count = 0
                page_number += 1
        
        # Add final page if there are remaining elements
        if current_page_elements:
            page_content = self._elements_to_content(current_page_elements)
            chapter_id = self._find_chapter_for_elements(current_page_elements, chapters)
            
            page = Page(
                page_number=page_number,
                content=page_content,
                word_count=current_page_word_count,
                elements=current_page_elements.copy(),
                chapter_id=chapter_id
            )
            pages.append(page)
        
        # Update chapter page ranges
        await self._update_chapter_page_ranges(chapters, pages)
        
        return pages
    
    async def _should_break_page(self, element: ContentElement, current_page_elements: List[ContentElement], max_words_per_page: int) -> bool:
        """Determine if a page break should occur based on sophisticated rules"""
        
        # Never break within avoided priority content
        if element.break_priority == PageBreakPriority.AVOIDED:
            return False
        
        # Always break for mandatory priority
        if element.break_priority == PageBreakPriority.MANDATORY:
            return True
        
        # Check section completeness - if element is part of a larger section
        if element.level in [HierarchyLevel.H5, HierarchyLevel.H6]:
            # Keep H5/H6 with their parent sections
            return False
        
        # Check if we're over 80% of page capacity
        current_word_count = sum(elem.word_count for elem in current_page_elements)
        if current_word_count / max_words_per_page > 0.8:
            return True
        
        # For preferred breaks, break if we're over 70% capacity
        if element.break_priority == PageBreakPriority.PREFERRED:
            if current_word_count / max_words_per_page > 0.7:
                return True
        
        return False
    
    async def _apply_orphan_widow_prevention(self, pages: List[Page], elements: List[ContentElement]) -> List[Page]:
        """Apply orphan and widow prevention rules"""
        # This is a simplified implementation
        # In a full implementation, we would analyze line breaks and adjust accordingly
        
        for page in pages:
            # Check for orphans (single lines at bottom of page)
            if len(page.elements) > 0:
                last_element = page.elements[-1]
                if last_element.word_count < self.orphan_widow_threshold:
                    page.has_orphan = True
            
            # Check for widows (single lines at top of page)
            if len(page.elements) > 0:
                first_element = page.elements[0]
                if first_element.word_count < self.orphan_widow_threshold:
                    page.has_widow = True
        
        return pages
    
    async def _generate_table_of_contents(self, chapters: List[Chapter], elements: List[ContentElement]) -> TableOfContents:
        """Generate table of contents from chapters and headings"""
        toc_entries = []
        
        for chapter in chapters:
            # Add chapter entry
            toc_entries.append({
                "title": chapter.title,
                "page": chapter.start_page + 1,
                "level": 1,
                "type": "chapter"
            })
            
            # Add section entries (H3 and H4 headings)
            for element in chapter.elements:
                if element.level in [HierarchyLevel.H3, HierarchyLevel.H4]:
                    level = 2 if element.level == HierarchyLevel.H3 else 3
                    toc_entries.append({
                        "title": element.content,
                        "page": 1,  # We'd need to calculate actual page numbers
                        "level": level,
                        "type": "section"
                    })
        
        return TableOfContents(entries=toc_entries)
    
    def _elements_to_content(self, elements: List[ContentElement]) -> str:
        """Convert elements to formatted content string"""
        content_parts = []
        
        for element in elements:
            if element.type == "heading":
                if element.level == HierarchyLevel.H1:
                    content_parts.append(f"# {element.content}")
                elif element.level == HierarchyLevel.H2:
                    content_parts.append(f"## {element.content}")
                elif element.level == HierarchyLevel.H3:
                    content_parts.append(f"### {element.content}")
                elif element.level == HierarchyLevel.H4:
                    content_parts.append(f"#### {element.content}")
                elif element.level == HierarchyLevel.H5:
                    content_parts.append(f"##### {element.content}")
                elif element.level == HierarchyLevel.H6:
                    content_parts.append(f"###### {element.content}")
            else:
                content_parts.append(element.content)
        
        return "\n\n".join(content_parts)
    
    def _find_chapter_for_elements(self, elements: List[ContentElement], chapters: List[Chapter]) -> Optional[str]:
        """Find which chapter contains these elements"""
        if not elements:
            return None
        
        for chapter in chapters:
            if any(elem.id in [ch_elem.id for ch_elem in chapter.elements] for elem in elements):
                return chapter.id
        
        return None
    
    async def _update_chapter_page_ranges(self, chapters: List[Chapter], pages: List[Page]):
        """Update chapter page ranges based on actual page generation"""
        for chapter in chapters:
            chapter_pages = [page for page in pages if page.chapter_id == chapter.id]
            if chapter_pages:
                chapter.start_page = chapter_pages[0].page_number - 1  # 0-indexed
                chapter.end_page = chapter_pages[-1].page_number - 1  # 0-indexed