import asyncio
import os
import uuid
from typing import List, Dict, Any, Optional
import google.generativeai as genai
from models.ebook import ContentElement, HierarchyLevel, PageBreakPriority
import re
import logging
import json

logger = logging.getLogger(__name__)

class GeminiEbookService:
    def __init__(self):
        self.api_key = os.environ.get('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        
        # Configure Google Generative AI
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
    
    async def analyze_and_structure_content(self, content: str, title: str) -> List[ContentElement]:
        """
        Analyze content and create sophisticated hierarchical structure with proper formatting
        """
        # System message defining the sophisticated formatting rules
        system_message = """You are an expert book editor and formatter specializing in creating professional ebooks with sophisticated hierarchical structure.

Your task is to analyze plain text content and structure it according to these professional publishing standards:

HIERARCHY LEVELS:
- H1: Book Title (Document Level) - 1 line - Always start new document
- H2: Chapter Title (Chapter Level) - 1-2 lines - Always start new page  
- H3: Major Section (Section Level) - 500-1500 words - Prefer new page, allow same page if <300 words remaining
- H4: Subsection (Subsection Level) - 200-800 words - Keep with parent H3, break if >80% page filled
- H5: Category/Step (Category Level) - 50-300 words - Keep with parent H4, break only if necessary
- H6: Implementation Detail (Detail Level) - 20-100 words - Keep with parent H5, never break alone

CONTENT ANALYSIS RULES:
1. Identify natural chapter boundaries based on topic shifts
2. Create logical section hierarchies (H3-H6) within chapters
3. Maintain content flow and readability
4. Ensure proper nesting (H4 must be under H3, H5 under H4, etc.)
5. Add appropriate headings where content structure suggests them

PAGE BREAK PRIORITIES:
- MANDATORY: Before H2 (Chapter titles), Before major story sections, Before conclusion sections
- PREFERRED: Before H3 (Major sections), Before framework tables, Before practical applications
- ALLOWED: Before H4 (Subsections), Between paragraphs within sections
- AVOIDED: Within H5/H6 content blocks, Within tables, Within closely related H4 subsections

RESPONSE FORMAT:
Return the structured content as a JSON array where each element represents a content piece with:
{
  "type": "heading" | "paragraph" | "list",
  "level": "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | null,
  "content": "actual content text",
  "break_priority": "mandatory" | "preferred" | "allowed" | "avoided",
  "page_break_before": true/false,
  "page_break_after": true/false
}

IMPORTANT: Only return the JSON array, no other text or explanation."""

        user_message = f"""Please analyze and structure this content for the ebook titled "{title}":

{content}

Apply sophisticated hierarchical formatting with proper H1-H6 levels, intelligent chapter divisions, and professional page break logic. Ensure the structure follows publishing standards for readability and professional presentation.

{system_message}"""

        try:
            # Use Google Generative AI directly
            response = await self._call_gemini_async(user_message)
            logger.info(f"Gemini response received: {response[:200]}...")
            
            # Parse the JSON response
            elements = await self._parse_gemini_response(response)
            
            # Post-process elements to ensure proper structure
            structured_elements = await self._post_process_elements(elements, title)
            
            return structured_elements
            
        except Exception as e:
            logger.error(f"Error in Gemini analysis: {str(e)}")
            # Fallback to basic parsing
            return await self._fallback_content_parsing(content, title)

    async def _call_gemini_async(self, message: str) -> str:
        """Call Gemini API asynchronously"""
        try:
            # Run the synchronous call in a thread pool
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, 
                lambda: self.model.generate_content(message)
            )
            return response.text
        except Exception as e:
            logger.error(f"Error calling Gemini API: {str(e)}")
            raise

    async def _parse_gemini_response(self, response: str) -> List[Dict[str, Any]]:
        """Parse Gemini's JSON response into structured elements"""
        try:
            # Clean the response to extract JSON
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            
            # Try to find JSON array in response
            import json
            
            # Look for JSON array pattern
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                elements = json.loads(json_str)
                return elements
            else:
                # Try parsing the entire response as JSON
                elements = json.loads(response)
                return elements
                
        except (json.JSONDecodeError, AttributeError) as e:
            logger.error(f"Failed to parse Gemini JSON response: {str(e)}")
            logger.error(f"Response was: {response}")
            raise

    async def _post_process_elements(self, elements: List[Dict[str, Any]], title: str) -> List[ContentElement]:
        """Post-process elements to ensure proper structure and add missing data"""
        processed_elements = []
        
        # Add book title as H1 if not present
        has_h1 = any(elem.get('level') == 'h1' for elem in elements)
        if not has_h1:
            title_element = ContentElement(
                type="heading",
                level=HierarchyLevel.H1,
                content=title,
                word_count=len(title.split()),
                break_priority=PageBreakPriority.MANDATORY,
                page_break_before=True,
                page_break_after=True
            )
            processed_elements.append(title_element)
        
        # Process each element
        for elem_data in elements:
            try:
                # Calculate word count
                content = elem_data.get('content', '')
                word_count = len(content.split())
                
                # Map string levels to enum
                level_str = elem_data.get('level')
                level = None
                if level_str:
                    level = HierarchyLevel(level_str.lower())
                
                # Map break priority
                priority_str = elem_data.get('break_priority', 'allowed')
                break_priority = PageBreakPriority(priority_str.lower())
                
                element = ContentElement(
                    type=elem_data.get('type', 'paragraph'),
                    level=level,
                    content=content,
                    word_count=word_count,
                    break_priority=break_priority,
                    page_break_before=elem_data.get('page_break_before', False),
                    page_break_after=elem_data.get('page_break_after', False)
                )
                
                processed_elements.append(element)
                
            except Exception as e:
                logger.error(f"Error processing element {elem_data}: {str(e)}")
                # Create a basic paragraph element as fallback
                content = elem_data.get('content', '')
                element = ContentElement(
                    type="paragraph",
                    content=content,
                    word_count=len(content.split()),
                    break_priority=PageBreakPriority.ALLOWED
                )
                processed_elements.append(element)
        
        return processed_elements

    async def _fallback_content_parsing(self, content: str, title: str) -> List[ContentElement]:
        """Fallback method for basic content parsing when Gemini fails"""
        logger.warning("Using fallback content parsing")
        elements = []
        
        # Add title as H1
        title_element = ContentElement(
            type="heading",
            level=HierarchyLevel.H1,
            content=title,
            word_count=len(title.split()),
            break_priority=PageBreakPriority.MANDATORY,
            page_break_before=True,
            page_break_after=True
        )
        elements.append(title_element)
        
        # Split content into paragraphs
        paragraphs = content.split('\n\n')
        chapter_count = 0
        
        for i, paragraph in enumerate(paragraphs):
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            word_count = len(paragraph.split())
            
            # Simple heuristic for chapters (long paragraphs or certain patterns)
            if word_count > 500 or i == 0:
                chapter_count += 1
                # Create chapter heading
                chapter_title = f"Chapter {chapter_count}"
                if word_count < 50:  # If it's a short paragraph, use it as chapter title
                    chapter_title = paragraph
                    elements.append(ContentElement(
                        type="heading",
                        level=HierarchyLevel.H2,
                        content=chapter_title,
                        word_count=len(chapter_title.split()),
                        break_priority=PageBreakPriority.MANDATORY,
                        page_break_before=True
                    ))
                    continue
                else:
                    elements.append(ContentElement(
                        type="heading",
                        level=HierarchyLevel.H2,
                        content=chapter_title,
                        word_count=len(chapter_title.split()),
                        break_priority=PageBreakPriority.MANDATORY,
                        page_break_before=True
                    ))
            
            # Add paragraph
            elements.append(ContentElement(
                type="paragraph",
                content=paragraph,
                word_count=word_count,
                break_priority=PageBreakPriority.ALLOWED
            ))
        
        return elements