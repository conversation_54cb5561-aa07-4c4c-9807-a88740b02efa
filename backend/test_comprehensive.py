#!/usr/bin/env python3

import asyncio
import requests
import json
import os
from dotenv import load_dotenv
from models.ebook import EbookRequest, ThemeType
from services.ebook_service import EbookGenerationService

# Load environment variables
load_dotenv()

async def test_service_directly():
    """Test the ebook service directly"""
    print("🔄 Testing ebook service directly...")
    
    try:
        request = EbookRequest(
            title="Direct Service Test",
            author="Test Author",
            content="""
            This is a comprehensive test of the ebook generation service.
            
            Chapter 1: Introduction
            This chapter introduces the main concepts and provides background information.
            It contains multiple paragraphs to test the content structuring.
            
            Chapter 2: Core Features
            This chapter covers the core features of the system.
            It includes detailed explanations and examples.
            
            Chapter 3: Advanced Topics
            This chapter delves into advanced topics and edge cases.
            It provides comprehensive coverage of complex scenarios.
            
            Chapter 4: Conclusion
            This chapter summarizes the key points and provides final thoughts.
            """,
            theme=ThemeType.MODERN,
            max_words_per_page=300
        )
        
        service = EbookGenerationService()
        result = await service.generate_ebook(request)
        
        print(f"✅ Direct service test successful!")
        print(f"📊 Generated {len(result.chapters)} chapters")
        print(f"📄 Generated {len(result.pages)} pages")
        print(f"📋 Table of contents has {len(result.table_of_contents.entries)} entries")
        
        # Verify table of contents structure
        for entry in result.table_of_contents.entries:
            assert isinstance(entry, dict), f"TOC entry should be dict, got {type(entry)}"
            assert "title" in entry, "TOC entry should have title"
            assert "page" in entry, "TOC entry should have page"
            assert "level" in entry, "TOC entry should have level"
            assert "type" in entry, "TOC entry should have type"
        
        print("✅ Table of contents structure validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Direct service test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoint():
    """Test the API endpoint"""
    print("🔄 Testing API endpoint...")
    
    url = "http://localhost:8000/api/generate-ebook"
    
    payload = {
        "title": "API Test Ebook",
        "author": "API Test Author",
        "content": """
        This is a comprehensive test of the API endpoint.
        
        Chapter 1: API Testing
        This chapter tests the API functionality with various content types.
        It includes multiple paragraphs and different structures.
        
        Chapter 2: Error Handling
        This chapter tests error handling and edge cases.
        It ensures the API responds correctly to different inputs.
        
        Chapter 3: Performance
        This chapter tests the performance characteristics.
        It includes longer content to test processing capabilities.
        """,
        "theme": "modern",
        "max_words_per_page": 300
    }
    
    try:
        response = requests.post(url, json=payload, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API test successful!")
            print(f"📊 Generated {len(data['chapters'])} chapters")
            print(f"📄 Generated {len(data['pages'])} pages")
            print(f"📋 Table of contents has {len(data['table_of_contents']['entries'])} entries")
            
            # Verify response structure
            assert "metadata" in data, "Response should have metadata"
            assert "chapters" in data, "Response should have chapters"
            assert "pages" in data, "Response should have pages"
            assert "table_of_contents" in data, "Response should have table_of_contents"
            
            # Verify table of contents structure
            toc_entries = data['table_of_contents']['entries']
            for entry in toc_entries:
                assert isinstance(entry, dict), f"TOC entry should be dict, got {type(entry)}"
                assert "title" in entry, "TOC entry should have title"
                assert "page" in entry, "TOC entry should have page"
                assert "level" in entry, "TOC entry should have level"
                assert "type" in entry, "TOC entry should have type"
            
            print("✅ API response structure validation passed")
            return True
        else:
            print(f"❌ API test failed with status {response.status_code}")
            print(f"❌ Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """Test edge cases that might cause the original error"""
    print("🔄 Testing edge cases...")
    
    test_cases = [
        {
            "name": "Empty content",
            "payload": {
                "title": "Empty Test",
                "author": "Test",
                "content": "",
                "theme": "modern"
            }
        },
        {
            "name": "Single paragraph",
            "payload": {
                "title": "Single Paragraph Test",
                "author": "Test",
                "content": "This is just a single paragraph with no chapters.",
                "theme": "classic"
            }
        },
        {
            "name": "No explicit chapters",
            "payload": {
                "title": "No Chapters Test",
                "author": "Test",
                "content": "This content has no explicit chapter markers. It should still work and create default chapters.",
                "theme": "elegant"
            }
        }
    ]
    
    url = "http://localhost:8000/api/generate-ebook"
    
    for test_case in test_cases:
        try:
            print(f"  Testing: {test_case['name']}")
            response = requests.post(url, json=test_case['payload'], timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                print(f"    ✅ {test_case['name']} passed")
            else:
                print(f"    ❌ {test_case['name']} failed: {response.status_code}")
                print(f"    Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"    ❌ {test_case['name']} failed with exception: {str(e)}")
            return False
    
    print("✅ All edge case tests passed")
    return True

async def main():
    """Run all tests"""
    print("🚀 Starting comprehensive ebook generation tests...\n")
    
    # Check if API key is available
    api_key = os.environ.get('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY environment variable not found")
        return False
    
    print("✅ GEMINI_API_KEY found\n")
    
    # Run tests
    tests = [
        ("Direct Service Test", test_service_directly()),
        ("API Endpoint Test", test_api_endpoint),
        ("Edge Cases Test", test_edge_cases)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        if asyncio.iscoroutine(test_func):
            result = await test_func
        else:
            result = test_func()
        
        results.append((test_name, result))
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\nOverall Result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    return all_passed

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
