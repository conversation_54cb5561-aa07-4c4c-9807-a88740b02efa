#!/usr/bin/env python3
import sys
import os
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from dotenv import load_dotenv
load_dotenv(dotenv_path=current_dir / '.env')

import asyncio
from services.gemini_service import GeminiEbookService

async def test_gemini():
    """Test the Gemini service with our new implementation"""
    try:
        # Check if API key is available
        api_key = os.environ.get('GEMINI_API_KEY')
        if not api_key:
            print("❌ GEMINI_API_KEY environment variable not found")
            return False
        
        print("✅ GEMINI_API_KEY found")
        
        # Create service instance
        gemini_service = GeminiEbookService()
        print("✅ GeminiEbookService created successfully")
        
        # Test with simple content
        test_content = "This is a test paragraph to verify Gemini AI integration. It contains some basic content that should be analyzed and structured properly."
        test_title = "Test Ebook"
        
        print("🔄 Testing content analysis...")
        elements = await gemini_service.analyze_and_structure_content(test_content, test_title)
        
        print(f"✅ Content analysis successful! Generated {len(elements)} elements")
        
        # Show first element
        if elements:
            first_elem = elements[0]
            print(f"📄 First element: {first_elem.type} - '{first_elem.content[:50]}...'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_gemini())
    if result:
        print("\n🎉 Gemini service test completed successfully!")
    else:
        print("\n💥 Gemini service test failed!")
        sys.exit(1) 