#!/usr/bin/env python3

import asyncio
import sys
import os
from dotenv import load_dotenv
from models.ebook import EbookRequest, ThemeType
from services.ebook_service import EbookGenerationService

# Load environment variables
load_dotenv()

async def test_ebook_generation():
    """Test ebook generation to reproduce the error"""
    
    # Check if API key is available
    api_key = os.environ.get('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY environment variable not found")
        return False
    
    print("✅ GEMINI_API_KEY found")
    
    try:
        # Create test request
        request = EbookRequest(
            title="Test Ebook",
            author="Test Author",
            content="""
            This is a test ebook content.
            
            Chapter 1: Introduction
            This is the introduction chapter with some content.
            
            Chapter 2: Main Content
            This is the main content chapter with more detailed information.
            
            Chapter 3: Conclusion
            This is the conclusion chapter that wraps up the content.
            """,
            theme=ThemeType.MODERN,
            max_words_per_page=300
        )
        
        print("🔄 Testing ebook generation...")
        
        # Create service and generate ebook
        service = EbookGenerationService()
        result = await service.generate_ebook(request)
        
        print(f"✅ Ebook generation successful!")
        print(f"📊 Generated {len(result.chapters)} chapters")
        print(f"📄 Generated {len(result.pages)} pages")
        print(f"📋 Table of contents has {len(result.table_of_contents.entries)} entries")
        
        # Print some details
        print("\n📖 Chapters:")
        for i, chapter in enumerate(result.chapters):
            print(f"  {i+1}. {chapter.title} (Pages: {chapter.start_page}-{chapter.end_page})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during ebook generation: {str(e)}")
        print(f"❌ Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_ebook_generation())
    sys.exit(0 if success else 1)
