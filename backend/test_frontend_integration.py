#!/usr/bin/env python3
"""
Integration test to verify the frontend can successfully generate ebooks
after the table of contents fix.
"""

import requests
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_frontend_integration():
    """Test that mimics what the frontend sends to the backend"""
    
    print("🔄 Testing frontend integration...")
    
    # This payload mimics what the frontend EbookGenerator.jsx sends
    url = "http://localhost:8000/api/generate-ebook"
    
    # Simulate the exact payload structure from frontend
    payload = {
        "title": "My Test Ebook",
        "author": "<PERSON>",
        "content": """Welcome to my ebook! This is an introduction to the amazing world of AI-generated content.

Chapter 1: Getting Started
In this chapter, we'll explore the basics of ebook generation. The process involves analyzing your content, structuring it into logical chapters, and formatting it for optimal reading experience.

The AI system can identify natural chapter breaks, create appropriate headings, and ensure proper page flow. This makes it easy to transform any text into a professional-looking ebook.

Chapter 2: Advanced Features
This chapter covers more advanced features of the ebook generation system. We'll look at how the AI handles different content types, manages page breaks, and creates table of contents.

The system also supports various themes and styling options, allowing you to customize the appearance of your ebook to match your preferences.

Chapter 3: Best Practices
Here are some best practices for creating great ebooks with AI assistance:

1. Provide clear, well-structured content
2. Use descriptive chapter titles
3. Include sufficient content for meaningful chapters
4. Review and edit the generated output

Chapter 4: Conclusion
AI-powered ebook generation represents a significant advancement in content creation tools. It democratizes publishing by making it easy for anyone to create professional-quality ebooks.

The future of content creation is bright, with AI assistants helping writers focus on creativity while handling the technical aspects of formatting and structure.""",
        "theme": "modern",
        "max_words_per_page": 300
    }
    
    try:
        print("📤 Sending request to backend...")
        response = requests.post(url, json=payload, timeout=90)
        
        if response.status_code != 200:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
        print("✅ Request successful!")
        
        # Parse response
        data = response.json()
        
        # Verify response structure matches what frontend expects
        required_fields = ['id', 'metadata', 'chapters', 'pages', 'table_of_contents', 'formatted_content', 'processing_log']
        for field in required_fields:
            if field not in data:
                print(f"❌ Missing required field: {field}")
                return False
        
        print("✅ Response structure validation passed")
        
        # Test metadata
        metadata = data['metadata']
        assert metadata['title'] == payload['title'], "Title mismatch"
        assert metadata['author'] == payload['author'], "Author mismatch"
        assert metadata['theme'] == payload['theme'], "Theme mismatch"
        print(f"✅ Metadata validation passed")
        
        # Test chapters
        chapters = data['chapters']
        assert len(chapters) > 0, "Should have at least one chapter"
        for chapter in chapters:
            required_chapter_fields = ['id', 'title', 'start_page', 'end_page', 'word_count', 'elements']
            for field in required_chapter_fields:
                assert field in chapter, f"Chapter missing field: {field}"
        print(f"✅ Chapters validation passed ({len(chapters)} chapters)")
        
        # Test pages
        pages = data['pages']
        assert len(pages) > 0, "Should have at least one page"
        for page in pages:
            required_page_fields = ['page_number', 'content', 'word_count', 'elements']
            for field in required_page_fields:
                assert field in page, f"Page missing field: {field}"
        print(f"✅ Pages validation passed ({len(pages)} pages)")
        
        # Test table of contents (this was the bug!)
        toc = data['table_of_contents']
        assert 'entries' in toc, "TOC should have entries"
        entries = toc['entries']
        assert len(entries) > 0, "TOC should have at least one entry"
        
        for entry in entries:
            # This is the critical test - the fix ensures these are accessible as dict keys
            required_toc_fields = ['title', 'page', 'level', 'type']
            for field in required_toc_fields:
                assert field in entry, f"TOC entry missing field: {field}"
                # Verify we can access the value
                value = entry[field]
                assert value is not None, f"TOC entry field {field} should not be None"
        
        print(f"✅ Table of contents validation passed ({len(entries)} entries)")
        
        # Test that frontend transformation would work
        # This simulates the transformation in EbookGenerator.jsx lines 89-112
        try:
            transformed_ebook = {
                "metadata": {
                    "title": data["metadata"]["title"],
                    "author": data["metadata"]["author"],
                    "theme": data["metadata"]["theme"],
                    "generatedAt": data["metadata"]["generated_at"],
                    "totalPages": data["metadata"]["total_pages"],
                    "totalChapters": data["metadata"]["total_chapters"],
                    "wordCount": data["metadata"]["word_count"]
                },
                "chapters": [
                    {
                        "id": chapter["id"],
                        "title": chapter["title"],
                        "startPage": chapter["start_page"],
                        "endPage": chapter["end_page"]
                    }
                    for chapter in data["chapters"]
                ],
                "pages": [
                    {
                        "pageNumber": page["page_number"],
                        "content": page["content"],
                        "wordCount": page["word_count"],
                        "chapter": page.get("chapter_id")
                    }
                    for page in data["pages"]
                ],
                "tableOfContents": data["table_of_contents"]["entries"] or []
            }
            
            print("✅ Frontend transformation simulation passed")
            
        except Exception as e:
            print(f"❌ Frontend transformation simulation failed: {str(e)}")
            return False
        
        # Print summary
        print(f"\n📊 Generation Summary:")
        print(f"   Title: {metadata['title']}")
        print(f"   Author: {metadata['author']}")
        print(f"   Theme: {metadata['theme']}")
        print(f"   Chapters: {len(chapters)}")
        print(f"   Pages: {len(pages)}")
        print(f"   TOC Entries: {len(entries)}")
        print(f"   Word Count: {metadata['word_count']}")
        print(f"   Processing Time: {metadata.get('processing_time', 'N/A')}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Frontend integration test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the frontend integration test"""
    print("🌐 Running frontend integration test...")
    print("This test verifies the complete frontend-to-backend flow works correctly.\n")
    
    # Check if API key is available
    api_key = os.environ.get('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY environment variable not found")
        return False
    
    success = test_frontend_integration()
    
    if success:
        print("\n🎉 Frontend integration test PASSED!")
        print("The frontend should now work correctly with the backend.")
    else:
        print("\n💥 Frontend integration test FAILED!")
        print("There may be issues with the frontend-backend integration.")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
