#!/usr/bin/env python3

import requests
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_api_request():
    """Test the actual API endpoint"""
    
    url = "http://localhost:8000/api/generate-ebook"
    
    payload = {
        "title": "Test Ebook",
        "author": "Test Author",
        "content": """
        This is a test ebook content.
        
        Chapter 1: Introduction
        This is the introduction chapter with some content.
        
        Chapter 2: Main Content
        This is the main content chapter with more detailed information.
        
        Chapter 3: Conclusion
        This is the conclusion chapter that wraps up the content.
        """,
        "theme": "modern",
        "max_words_per_page": 300
    }
    
    try:
        print("🔄 Making API request...")
        response = requests.post(url, json=payload, timeout=60)
        
        if response.status_code == 200:
            print("✅ API request successful!")
            data = response.json()
            print(f"📊 Generated {len(data['chapters'])} chapters")
            print(f"📄 Generated {len(data['pages'])} pages")
            print(f"📋 Table of contents has {len(data['table_of_contents']['entries'])} entries")
            return True
        else:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"❌ Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during API request: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_api_request()
    exit(0 if success else 1)
