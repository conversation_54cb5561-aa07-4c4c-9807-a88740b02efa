#!/usr/bin/env python3
import requests
import json
import time
import os
import sys
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables from frontend/.env
frontend_env_path = Path('/app/frontend/.env')
load_dotenv(frontend_env_path)

# Get backend URL from environment
BACKEND_URL = os.environ.get('REACT_APP_BACKEND_URL')
if not BACKEND_URL:
    print("Error: REACT_APP_BACKEND_URL not found in frontend/.env")
    sys.exit(1)

# Ensure the URL ends with /api
API_URL = f"{BACKEND_URL}/api"
print(f"Using API URL: {API_URL}")

# Sample content for testing
SAMPLE_CONTENT = """Welcome to the Future of Technology

The rapid advancement of technology has transformed our world in unprecedented ways. From artificial intelligence to quantum computing, we are witnessing a revolution that touches every aspect of human life.

Understanding Artificial Intelligence

Artificial Intelligence represents one of the most significant technological breakthroughs of our time. It encompasses machine learning, natural language processing, and computer vision, enabling machines to perform tasks that traditionally required human intelligence.

Machine learning algorithms can analyze vast amounts of data to identify patterns and make predictions. This capability has revolutionized industries from healthcare to finance, enabling more accurate diagnoses and better investment decisions.

The Impact on Society

The integration of AI into various sectors has brought both opportunities and challenges. While it has increased efficiency and opened new possibilities, it has also raised concerns about job displacement and privacy.

Healthcare has seen remarkable improvements through AI-powered diagnostic tools and personalized treatment plans. Doctors can now detect diseases earlier and provide more targeted therapies.

Looking Ahead

As we advance into the future, the responsible development and deployment of AI will be crucial. We must balance innovation with ethical considerations to ensure that technology serves humanity's best interests."""

# Test results tracking
test_results = {
    "health_check": {"status": "not_tested", "details": ""},
    "test_gemini": {"status": "not_tested", "details": ""},
    "generate_ebook": {"status": "not_tested", "details": ""},
    "get_generation": {"status": "not_tested", "details": ""},
    "list_generations": {"status": "not_tested", "details": ""}
}

def print_separator():
    print("\n" + "="*80 + "\n")

def test_health_check():
    """Test the health check endpoint"""
    print("Testing health check endpoint...")
    try:
        response = requests.get(f"{API_URL}/")
        if response.status_code == 200:
            data = response.json()
            if data.get("message") == "AI Ebook Generator API is running":
                test_results["health_check"]["status"] = "passed"
                test_results["health_check"]["details"] = "Health check endpoint is working correctly"
                print("✅ Health check test passed")
                return True
            else:
                test_results["health_check"]["status"] = "failed"
                test_results["health_check"]["details"] = f"Unexpected response: {data}"
                print("❌ Health check test failed: Unexpected response")
        else:
            test_results["health_check"]["status"] = "failed"
            test_results["health_check"]["details"] = f"Status code: {response.status_code}, Response: {response.text}"
            print(f"❌ Health check test failed: Status code {response.status_code}")
    except Exception as e:
        test_results["health_check"]["status"] = "failed"
        test_results["health_check"]["details"] = f"Exception: {str(e)}"
        print(f"❌ Health check test failed: {str(e)}")
    return False

def test_gemini_connection():
    """Test the Gemini AI connection"""
    print("Testing Gemini AI connection...")
    try:
        response = requests.post(f"{API_URL}/test-gemini")
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                test_results["test_gemini"]["status"] = "passed"
                test_results["test_gemini"]["details"] = f"Gemini connection successful: {data.get('message')}"
                print("✅ Gemini connection test passed")
                return True
            else:
                test_results["test_gemini"]["status"] = "failed"
                test_results["test_gemini"]["details"] = f"Gemini connection failed: {data.get('message')}"
                print(f"❌ Gemini connection test failed: {data.get('message')}")
        else:
            test_results["test_gemini"]["status"] = "failed"
            test_results["test_gemini"]["details"] = f"Status code: {response.status_code}, Response: {response.text}"
            print(f"❌ Gemini connection test failed: Status code {response.status_code}")
    except Exception as e:
        test_results["test_gemini"]["status"] = "failed"
        test_results["test_gemini"]["details"] = f"Exception: {str(e)}"
        print(f"❌ Gemini connection test failed: {str(e)}")
    return False

def test_generate_ebook():
    """Test the ebook generation endpoint"""
    print("Testing ebook generation endpoint...")
    
    # Test with short content
    short_content = "This is a short test paragraph for ebook generation."
    generation_id = None
    
    try:
        # Test with short content first
        print("Testing with short content...")
        payload = {
            "title": "Test Ebook - Short Content",
            "author": "Test Author",
            "content": short_content,
            "theme": "modern",
            "max_words_per_page": 300
        }
        
        response = requests.post(f"{API_URL}/generate-ebook", json=payload)
        if response.status_code == 200:
            data = response.json()
            generation_id = data.get("id")
            print(f"✅ Short content generation successful, ID: {generation_id}")
            
            # Now test with the full sample content
            print("Testing with full sample content...")
            payload = {
                "title": "The Future of Technology",
                "author": "Test Author",
                "content": SAMPLE_CONTENT,
                "theme": "academic",
                "max_words_per_page": 300
            }
            
            response = requests.post(f"{API_URL}/generate-ebook", json=payload)
            if response.status_code == 200:
                data = response.json()
                generation_id = data.get("id")
                
                # Verify the response structure
                if (
                    "metadata" in data and
                    "chapters" in data and
                    "pages" in data and
                    "table_of_contents" in data and
                    "formatted_content" in data
                ):
                    # Check if chapters were created
                    if len(data["chapters"]) > 0:
                        # Check if pages were created
                        if len(data["pages"]) > 0:
                            test_results["generate_ebook"]["status"] = "passed"
                            test_results["generate_ebook"]["details"] = (
                                f"Ebook generation successful. "
                                f"Generated {len(data['chapters'])} chapters and {len(data['pages'])} pages."
                            )
                            print("✅ Ebook generation test passed")
                            return True, generation_id
                        else:
                            test_results["generate_ebook"]["status"] = "failed"
                            test_results["generate_ebook"]["details"] = "No pages were generated"
                            print("❌ Ebook generation test failed: No pages were generated")
                    else:
                        test_results["generate_ebook"]["status"] = "failed"
                        test_results["generate_ebook"]["details"] = "No chapters were generated"
                        print("❌ Ebook generation test failed: No chapters were generated")
                else:
                    test_results["generate_ebook"]["status"] = "failed"
                    test_results["generate_ebook"]["details"] = "Response missing required fields"
                    print("❌ Ebook generation test failed: Response missing required fields")
            else:
                test_results["generate_ebook"]["status"] = "failed"
                test_results["generate_ebook"]["details"] = f"Status code: {response.status_code}, Response: {response.text}"
                print(f"❌ Ebook generation test failed: Status code {response.status_code}")
        else:
            test_results["generate_ebook"]["status"] = "failed"
            test_results["generate_ebook"]["details"] = f"Status code: {response.status_code}, Response: {response.text}"
            print(f"❌ Ebook generation test failed: Status code {response.status_code}")
    except Exception as e:
        test_results["generate_ebook"]["status"] = "failed"
        test_results["generate_ebook"]["details"] = f"Exception: {str(e)}"
        print(f"❌ Ebook generation test failed: {str(e)}")
    
    return False, generation_id

def test_get_generation(generation_id):
    """Test the get generation status endpoint"""
    if not generation_id:
        test_results["get_generation"]["status"] = "skipped"
        test_results["get_generation"]["details"] = "No generation ID available to test with"
        print("⚠️ Get generation test skipped: No generation ID available")
        return False
    
    print(f"Testing get generation status endpoint with ID: {generation_id}...")
    try:
        response = requests.get(f"{API_URL}/generation/{generation_id}")
        if response.status_code == 200:
            data = response.json()
            if "id" in data and "status" in data and "title" in data:
                test_results["get_generation"]["status"] = "passed"
                test_results["get_generation"]["details"] = f"Generation status retrieved successfully: {data['status']}"
                print(f"✅ Get generation test passed: Status is {data['status']}")
                return True
            else:
                test_results["get_generation"]["status"] = "failed"
                test_results["get_generation"]["details"] = "Response missing required fields"
                print("❌ Get generation test failed: Response missing required fields")
        else:
            test_results["get_generation"]["status"] = "failed"
            test_results["get_generation"]["details"] = f"Status code: {response.status_code}, Response: {response.text}"
            print(f"❌ Get generation test failed: Status code {response.status_code}")
    except Exception as e:
        test_results["get_generation"]["status"] = "failed"
        test_results["get_generation"]["details"] = f"Exception: {str(e)}"
        print(f"❌ Get generation test failed: {str(e)}")
    return False

def test_list_generations():
    """Test the list generations endpoint"""
    print("Testing list generations endpoint...")
    try:
        response = requests.get(f"{API_URL}/generations")
        if response.status_code == 200:
            data = response.json()
            if "generations" in data and isinstance(data["generations"], list):
                test_results["list_generations"]["status"] = "passed"
                test_results["list_generations"]["details"] = f"Retrieved {len(data['generations'])} generations"
                print(f"✅ List generations test passed: Retrieved {len(data['generations'])} generations")
                return True
            else:
                test_results["list_generations"]["status"] = "failed"
                test_results["list_generations"]["details"] = "Response missing 'generations' list"
                print("❌ List generations test failed: Response missing 'generations' list")
        else:
            test_results["list_generations"]["status"] = "failed"
            test_results["list_generations"]["details"] = f"Status code: {response.status_code}, Response: {response.text}"
            print(f"❌ List generations test failed: Status code {response.status_code}")
    except Exception as e:
        test_results["list_generations"]["status"] = "failed"
        test_results["list_generations"]["details"] = f"Exception: {str(e)}"
        print(f"❌ List generations test failed: {str(e)}")
    return False

def test_error_handling():
    """Test error handling for invalid requests"""
    print("Testing error handling...")
    
    # Test missing required fields
    try:
        payload = {
            # Missing title
            "author": "Test Author",
            "content": "Test content"
        }
        
        response = requests.post(f"{API_URL}/generate-ebook", json=payload)
        if response.status_code in [400, 422]:
            print("✅ Error handling test passed: Properly rejected request with missing title")
        else:
            print(f"❌ Error handling test failed: Expected 400/422 status code, got {response.status_code}")
    except Exception as e:
        print(f"❌ Error handling test failed: {str(e)}")

def run_all_tests():
    """Run all tests and print results"""
    print_separator()
    print("Starting AI Ebook Generator API Tests")
    print_separator()
    
    # Test health check
    health_check_passed = test_health_check()
    print_separator()
    
    # Test Gemini connection
    gemini_passed = test_gemini_connection()
    print_separator()
    
    # Test ebook generation
    ebook_passed, generation_id = test_generate_ebook()
    print_separator()
    
    # Test get generation status
    if generation_id:
        get_generation_passed = test_get_generation(generation_id)
    else:
        print("⚠️ Skipping get generation test due to missing generation ID")
        test_results["get_generation"]["status"] = "skipped"
        test_results["get_generation"]["details"] = "No generation ID available to test with"
    print_separator()
    
    # Test list generations
    list_generations_passed = test_list_generations()
    print_separator()
    
    # Test error handling
    test_error_handling()
    print_separator()
    
    # Print summary
    print("Test Results Summary:")
    for test_name, result in test_results.items():
        status = result["status"]
        if status == "passed":
            print(f"✅ {test_name}: PASSED")
        elif status == "failed":
            print(f"❌ {test_name}: FAILED - {result['details']}")
        else:
            print(f"⚠️ {test_name}: {status.upper()} - {result['details']}")
    
    print_separator()
    
    # Overall result
    all_passed = all(result["status"] == "passed" for result in test_results.values())
    if all_passed:
        print("🎉 All tests PASSED!")
    else:
        print("❌ Some tests FAILED. See details above.")
    
    return all_passed

if __name__ == "__main__":
    run_all_tests()