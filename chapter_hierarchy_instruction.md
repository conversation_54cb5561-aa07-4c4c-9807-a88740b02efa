# Chapter Hierarchy Guide for Ebook Processing
## The Prompt Manager's Playbook

### Document Purpose
This guide provides AI systems with the structural framework needed to parse, process, and distribute chapter content for ebook creation. It defines how content should be organized, paginated, and formatted to ensure proper ebook output.

---

## 1. Chapter Structure Hierarchy

### 1.1 Primary Hierarchy Levels

```
H1: Book Title (Document Level)
├── H2: Chapter Title (Chapter Level)
    ├── H3: Major Section (Section Level)
        ├── H4: Subsection (Subsection Level)
            ├── H5: Category/Process Step (Category Level)
                ├── H6: Implementation Detail (Detail Level)
```

### 1.2 Content Classification by Hierarchy Level

| Level | Element | Content Type | Typical Length | Page Break Rules |
|-------|---------|-------------|----------------|------------------|
| H1 | Book Title | Document identifier | 1 line | Always start new document |
| H2 | Chapter Title | Chapter identifier | 1-2 lines | Always start new page |
| H3 | Major Section | Primary topic/story | 500-1500 words | Prefer new page, allow same page if <300 words remaining |
| H4 | Subsection | Supporting topic/process | 200-800 words | Keep with parent H3, break if >80% page filled |
| H5 | Category/Step | Specific items/steps | 50-300 words | Keep with parent H4, break only if necessary |
| H6 | Detail | Implementation specifics | 20-100 words | Keep with parent H5, never break alone |

---

## 2. Content Distribution Rules

### 2.1 Section Completeness Rules

**Primary Rule**: A section should never be split across pages unless it exceeds the maximum page capacity.

**Implementation Logic**:
```
IF section_length + current_page_content > page_capacity:
    IF section_length > page_capacity:
        // Split section at natural break points
        SPLIT_AT_PARAGRAPH_BOUNDARY()
    ELSE:
        // Move entire section to next page
        START_NEW_PAGE()
        ADD_SECTION_TO_NEW_PAGE()
```

### 2.2 Page Break Priority (High to Low)

1. **Mandatory Breaks**
   - Before H2 (Chapter titles)
   - Before major story sections (when identified)
   - Before conclusion sections

2. **Preferred Breaks**
   - Before H3 (Major sections)
   - Before framework tables
   - Before practical application sections

3. **Allowed Breaks**
   - Before H4 (Subsections)
   - Between paragraphs within sections
   - Before/after tables and figures

4. **Avoided Breaks**
   - Within H5/H6 content blocks
   - Within tables
   - Within code blocks or checklists
   - Between closely related H4 subsections

### 2.3 Orphan and Widow Prevention

**Orphan Prevention** (Avoid single lines at bottom of page):
- Minimum 3 lines of content before page break
- If heading has <3 lines following, move to next page

**Widow Prevention** (Avoid single lines at top of page):
- Minimum 3 lines of content on continuation page
- If <3 lines remain, keep content on previous page

---

## 3. Content Type Handling

### 3.1 Narrative Content (Stories, Examples)

**Characteristics**:
- Usually found in H3 sections
- Contains dialogue and personal anecdotes
- Flows naturally in paragraphs

**Distribution Rules**:
```
- Keep story sections intact when possible
- If split required, break at natural story transitions
- Never break within dialogue
- Preserve story flow and continuity
```

### 3.2 Framework Content (Tables, Processes)

**Characteristics**:
- Structured data in tables
- Step-by-step processes
- Comparison matrices

**Distribution Rules**:
```
- Keep entire tables on single page if possible
- If table too large, split by logical row groups
- Maintain table headers on continuation pages
- Keep process steps grouped logically
```

### 3.3 Action Items and Checklists

**Characteristics**:
- Bulleted or numbered lists
- Checkbox-style formatting
- Short, actionable items

**Distribution Rules**:
```
- Keep checklist sections together
- If split required, break at major category boundaries
- Maintain consistent formatting across pages
- Preserve checkbox/bullet formatting
```

---

## 4. Special Content Handling

### 4.1 Emoji Headings

**Pattern**: `🎯 Heading Title`

**Processing Rules**:
- Treat emoji as integral part of heading
- Maintain emoji formatting in all outputs
- Consider emoji when calculating heading length

### 4.2 Quote Blocks and Callouts

**Pattern**: Italicized quotes, highlighted text boxes

**Processing Rules**:
- Keep quotes with their context
- Avoid breaking quote blocks
- Maintain special formatting

### 4.3 Page Markers

**Pattern**: `*Page X*`

**Processing Rules**:
- Remove existing page markers during processing
- Generate new page markers based on actual pagination
- Place at natural page boundaries

---

## 5. Page Capacity Guidelines

### 5.1 Standard Page Metrics

```
Estimated Words per Page: 250-300 words
Estimated Lines per Page: 25-30 lines
Page Capacity Calculation:
- Text content: 80% of page
- Headings: 15% of page
- Whitespace/margins: 5% of page
```

### 5.2 Content Density Adjustments

| Content Type | Density Factor | Adjustment |
|-------------|----------------|------------|
| Dense text paragraphs | 1.0x | Standard capacity |
| Tables/frameworks | 1.5x | Reduce capacity by 33% |
| Code blocks | 1.3x | Reduce capacity by 23% |
| Checklists | 1.2x | Reduce capacity by 17% |
| Mixed content | 1.1x | Reduce capacity by 9% |

---

## 6. AI Processing Instructions

### 6.1 Parsing Algorithm

```
1. IDENTIFY_CHAPTER_BOUNDARIES()
   - Locate H2 headings
   - Mark chapter start/end points

2. ANALYZE_SECTION_STRUCTURE()
   - Map H3-H6 hierarchy
   - Identify content types
   - Calculate section lengths

3. DETERMINE_BREAK_POINTS()
   - Apply page capacity rules
   - Consider content type factors
   - Optimize for readability

4. DISTRIBUTE_CONTENT()
   - Assign content to pages
   - Handle spillover sections
   - Maintain hierarchy integrity

5. VALIDATE_OUTPUT()
   - Check for orphans/widows
   - Verify section completeness
   - Confirm heading hierarchy
```

### 6.2 Decision Matrix for Content Splitting

```
IF content_fits_on_current_page:
    PLACE_ON_CURRENT_PAGE()
    
ELSE IF content_is_less_than_page_capacity:
    START_NEW_PAGE()
    PLACE_ENTIRE_CONTENT()
    
ELSE:
    FIND_NATURAL_BREAK_POINT()
    IF natural_break_exists:
        SPLIT_AT_NATURAL_BREAK()
    ELSE:
        SPLIT_AT_PARAGRAPH_BOUNDARY()
        ADD_CONTINUATION_MARKER()
```

---

## 7. Quality Assurance Checks

### 7.1 Post-Processing Validation

**Structural Integrity**:
- [ ] All headings properly nested
- [ ] No orphaned content
- [ ] Section continuity maintained
- [ ] Page breaks at appropriate locations

**Content Completeness**:
- [ ] All original content included
- [ ] No content duplication
- [ ] Proper formatting preserved
- [ ] Special elements (tables, lists) intact

**Readability**:
- [ ] Logical flow maintained
- [ ] No awkward page breaks
- [ ] Consistent formatting
- [ ] Appropriate white space

### 7.2 Error Handling

**Common Issues and Solutions**:

| Issue | Detection | Solution |
|-------|-----------|----------|
| Orphaned heading | H3-H6 at page bottom with no content | Move heading to next page |
| Broken table | Table split inappropriately | Keep table intact or split at logical boundaries |
| Disconnected content | Content separated from its context | Reorganize to maintain context |
| Inconsistent formatting | Formatting changes across pages | Standardize formatting throughout |

---

## 8. Output Specifications

### 8.1 Ebook Format Considerations

**EPUB Requirements**:
- Chapter files should be 250KB-300KB maximum
- Maintain HTML hierarchy structure
- Include navigation landmarks
- Preserve CSS styling classes

**PDF Requirements**:
- Consistent page dimensions
- Proper header/footer placement
- Maintained font hierarchy
- Appropriate margins and spacing

### 8.2 Cross-Reference Handling

**Internal Links**:
- Update page references after pagination
- Maintain chapter cross-references
- Preserve table of contents accuracy

**Navigation**:
- Generate chapter navigation
- Create section bookmarks
- Maintain heading-based navigation

---

## 9. Implementation Checklist

### 9.1 Pre-Processing
- [ ] Parse chapter structure
- [ ] Identify content types
- [ ] Calculate section lengths
- [ ] Determine break points

### 9.2 Processing
- [ ] Apply distribution rules
- [ ] Handle special content
- [ ] Maintain hierarchy
- [ ] Generate page breaks

### 9.3 Post-Processing
- [ ] Validate structure
- [ ] Check completeness
- [ ] Verify formatting
- [ ] Generate navigation

### 9.4 Final Output
- [ ] Export to target format
- [ ] Verify cross-references
- [ ] Test navigation
- [ ] Confirm readability

---

## 10. Success Metrics

A successful ebook processing should achieve:
- **95%+ content preservation** (no lost content)
- **<5% orphan/widow occurrences** (clean page breaks)
- **100% hierarchy maintenance** (proper heading structure)
- **Consistent formatting** (uniform appearance)
- **Readable flow** (logical content progression)

This guide ensures that AI systems can process "The Prompt Manager's Playbook" chapters effectively, creating professional, readable ebooks that maintain content integrity and optimal user experience.